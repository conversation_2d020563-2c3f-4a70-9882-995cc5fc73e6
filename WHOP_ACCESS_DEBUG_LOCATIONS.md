# Whop Access Level Debug Logging

## Overview
This document lists all the locations where prominent debug logs have been added to track Whop access level checks and their responses.

## 🚨 Debug Log Locations

### 1. Primary Access Check - User Access API
**File**: `api/user/access.js`
**Function**: Main user access endpoint
**Whop SDK Calls**:
- `whopSdk.access.checkIfUserHasAccessToExperience()`
- `whopSdk.access.checkIfUserHasAccessToCompany()` (fallback)

**Debug Patterns**:
- 🚨 WHOP ACCESS CHECK - EXPERIENCE LEVEL 🚨
- 🚨 WHOP ACCESS CHECK - COMPANY LEVEL (NO EXPERIENCE ID) 🚨  
- 🚨 WHOP ACCESS CHECK - COMPANY LEVEL (FALLBACK) 🚨

### 2. Direct Access Check API
**File**: `api/whop/check-access.js`
**Function**: Direct access check endpoint
**Whop SDK Call**: `whopSdk.access.checkIfUserHasAccessToExperience()`

**Debug Pattern**:
- 🚨 WHOP ACCESS CHECK - DIRECT API CALL 🚨

### 3. Product/Access Pass Check
**File**: `api/whop/check-product-access.js`
**Function**: Product access verification
**Whop SDK Call**: `whopSdk.access.checkIfUserHasAccessToAccessPass()`

**Debug Pattern**:
- 🚨 WHOP ACCESS CHECK - PRODUCT/ACCESS PASS LEVEL 🚨

### 4. Supabase Edge Function
**File**: `supabase/functions/whop-integration/index.ts`
**Function**: Supabase edge function access check
**Whop SDK Call**: `whopSdk.access.checkIfUserHasAccessToExperience()`

**Debug Pattern**:
- 🚨 WHOP ACCESS CHECK - SUPABASE EDGE FUNCTION 🚨

### 5. Agent Distribution Function
**File**: `supabase/functions/whop-distribute-agent/index.ts`
**Function**: Agent distribution access verification
**Whop SDK Call**: `whopSdk.access.checkIfUserHasAccessToCompany()`

**Debug Pattern**:
- 🚨 WHOP ACCESS CHECK - AGENT DISTRIBUTION 🚨

### 6. Client-Side Intermediary Client
**File**: `src/lib/whopIntermediaryClient.ts`
**Function**: Client-side access check via intermediary
**API Call**: Calls `/user/access` endpoint

**Debug Pattern**:
- 🔄 CLIENT-SIDE ACCESS CHECK REQUEST 🔄
- ✅ CLIENT-SIDE ACCESS CHECK RESPONSE ✅

### 7. Shared Client-Side Intermediary Client
**File**: `src/shared/src/lib/whopIntermediaryClient.ts`
**Function**: Shared client-side access check
**API Call**: Calls `/user/access` endpoint

**Debug Pattern**:
- 🔄 SHARED CLIENT-SIDE ACCESS CHECK REQUEST 🔄
- ✅ SHARED CLIENT-SIDE ACCESS CHECK RESPONSE ✅

## 📊 What Each Debug Log Shows

### Request Information
- **User ID**: The Whop user identifier
- **Experience ID**: The specific experience being checked (if available)
- **Company ID**: The Whop company identifier (for company-level checks)
- **Product/Access Pass ID**: For product-specific access checks
- **Timestamp**: When the request was made
- **Endpoint/Function**: Which API endpoint or function is making the call

### Response Information
- **Has Access**: Boolean indicating if user has access
- **Access Level**: One of `'admin'`, `'customer'`, or `'no_access'`
- **Full Response**: Complete JSON response from Whop SDK
- **Context**: Additional context about the request type

## 🔍 How to Use These Logs

### Finding Access Checks in Logs
Look for these distinctive patterns in your console/logs:
- Lines of `=` characters (80 characters wide)
- 🚨 emoji for server-side Whop SDK calls
- 🔄 emoji for client-side requests
- ✅ emoji for successful responses
- ⚠️ emoji for fallback scenarios

### Understanding the Flow
1. **Client Request**: Look for 🔄 patterns showing client-side requests
2. **Server Processing**: Look for 🚨 patterns showing server-side Whop SDK calls
3. **Response**: Look for 🎉 or ✅ patterns showing the results
4. **Fallbacks**: Look for ⚠️ patterns when experience access fails and company access is tried

### Key Information to Monitor
- **Access Level Changes**: Watch for transitions between `admin`, `customer`, and `no_access`
- **Fallback Triggers**: When experience access returns `no_access` and company access is attempted
- **Response Consistency**: Ensure the same user gets consistent access levels across different endpoints

## 🚀 Example Log Output

```
================================================================================
🚨 WHOP ACCESS CHECK - EXPERIENCE LEVEL 🚨
================================================================================
📋 REQUEST DETAILS:
   User ID: user_abc123
   Experience ID: exp_xyz789
   Timestamp: 2024-01-15T10:30:00.000Z
🔄 Calling whopSdk.access.checkIfUserHasAccessToExperience...
================================================================================

================================================================================
🎉 WHOP ACCESS RESPONSE - EXPERIENCE LEVEL 🎉
================================================================================
📊 RESPONSE DETAILS:
   Has Access: true
   Access Level: admin
   User ID: user_abc123
   Experience ID: exp_xyz789
   Full Response: {
     "hasAccess": true,
     "accessLevel": "admin"
   }
================================================================================
```

## 🔧 Troubleshooting

### If You Don't See Debug Logs
1. Check that the access check functions are being called
2. Verify console logging is enabled in your environment
3. Look for the specific emoji patterns (🚨, 🔄, ✅)

### If Access Levels Are Unexpected
1. Look for the "Full Response" in the debug logs
2. Check if fallback scenarios are being triggered (⚠️ patterns)
3. Verify the User ID and Experience ID being passed to Whop

### Performance Impact
These debug logs are verbose and should be removed or made conditional for production environments.
