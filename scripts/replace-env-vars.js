#!/usr/bin/env node

/**
 * Environment variable replacement script for build process
 * This script ensures environment variables are properly set for the build
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Running environment variable replacement script...');

// Check if we're in a Vercel environment
const isVercel = process.env.VERCEL === '1';
const isProduction = process.env.NODE_ENV === 'production';

console.log('Environment info:', {
  isVercel,
  isProduction,
  nodeEnv: process.env.NODE_ENV,
  vercelEnv: process.env.VERCEL_ENV
});

// List of required environment variables (centralized)
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_WHOP_APP_ID',
  'VITE_WHOP_AGENT_USER_ID',
  'VITE_WHOP_COMPANY_ID',
  'VITE_WHOP_INTERMEDIARY_URL'
];

// Check for missing environment variables
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.warn('⚠️  Missing environment variables:', missingVars);
  console.warn('Build will continue, but some features may not work properly.');
} else {
  console.log('✅ All required environment variables are present');
}

// Log environment variables (safely, without exposing secrets)
console.log('Environment variables status:');
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // Show first 10 characters and last 4 characters for keys/URLs
    const masked = value.length > 20 
      ? `${value.substring(0, 10)}...${value.substring(value.length - 4)}`
      : value.substring(0, 20) + '...';
    console.log(`  ${varName}: ${masked}`);
  } else {
    console.log(`  ${varName}: ❌ Missing`);
  }
});

// Create a simple .env.local file if it doesn't exist (for local development)
const envLocalPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envLocalPath) && !isVercel) {
  console.log('📝 Creating .env.local template...');
  const envTemplate = `# Local environment variables
# Copy this file and fill in your actual values

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Whop Configuration (Required)
VITE_WHOP_APP_ID=your_whop_app_id_here
VITE_WHOP_AGENT_USER_ID=your_whop_agent_user_id_here
VITE_WHOP_COMPANY_ID=your_whop_company_id_here
VITE_WHOP_INTERMEDIARY_URL=your_whop_intermediary_url_here
WHOP_API_KEY=your_whop_api_key_here

# Optional Whop Configuration for Multiple Apps
VITE_WHOP_INTERMEDIARY_PRODUCTION_URL=your_production_intermediary_url_here
VITE_TRADING_WHOP_APP_ID=your_trading_whop_app_id_here
VITE_TRADING_WHOP_AGENT_USER_ID=your_trading_whop_agent_user_id_here
VITE_TRADING_WHOP_COMPANY_ID=your_trading_whop_company_id_here
`;

  try {
    fs.writeFileSync(envLocalPath, envTemplate);
    console.log('✅ Created .env.local template');
  } catch (error) {
    console.warn('⚠️  Could not create .env.local template:', error.message);
  }
}

console.log('🚀 Environment variable replacement script completed');
console.log('Proceeding with build...\n');
