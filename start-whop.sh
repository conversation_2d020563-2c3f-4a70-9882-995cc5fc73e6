#!/bin/bash

# Start the Trade App with Whop Proxy
# This script starts the Vite dev server and Whop proxy concurrently

echo "🚀 Starting Trade App with Whop Proxy..."
echo ""

# Check if required ports are available
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Check required ports
echo "🔍 Checking required ports..."
check_port 3000 || echo "   (Whop proxy port)"
check_port 5173 || echo "   (Vite dev server port)"

echo ""
echo "📋 Starting services..."

# Start both services concurrently
echo "🔄 Starting Vite dev server and Whop proxy..."
npm run dev &
VITE_PID=$!
echo "   Vite PID: $VITE_PID"

npm run dev:whop &
WHOP_PID=$!
echo "   Whop Proxy PID: $WHOP_PID"

echo ""
echo "✅ Services started!"
echo "🌐 App will be available at: http://localhost:3000"
echo "🔧 Direct Vite server at: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $VITE_PID 2>/dev/null
    kill $WHOP_PID 2>/dev/null
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for both processes
wait
