import React, { useState, useEffect, memo } from 'react';
import SimpleTradingView<PERSON>hart from './SimpleTradingViewChart';
import FallbackChart from './FallbackChart';

interface RobustTradingViewChartProps {
  symbol: string;
  interval?: string;
  theme?: 'light' | 'dark';
  width?: string | number;
  height?: string | number;
  timezone?: string;
  range?: string;
  startDate?: string;
  endDate?: string;
}

const RobustTradingViewChart: React.FC<RobustTradingViewChartProps> = memo((props) => {
  const [showFallback, setShowFallback] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Set a timeout to show fallback if iframe doesn't load
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Check if we should show fallback (you can add more sophisticated checks here)
      // For now, we'll try the iframe first
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleIframeError = () => {
    console.log('TradingView iframe failed to load, showing fallback');
    setShowFallback(true);
    setIsLoading(false);
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  if (showFallback) {
    return <FallbackChart {...props} />;
  }

  return (
    <div className="relative">
      {isLoading && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-[#0A0A0C] border border-[#1A1A1C] rounded-lg z-10"
          style={{ 
            width: typeof props.width === 'string' ? props.width : `${props.width}px`,
            height: typeof props.height === 'string' ? props.height : `${props.height}px`,
            minHeight: '400px'
          }}
        >
          <div className="flex flex-col items-center">
            <div className="h-8 w-8 border-2 border-white/10 border-t-white/30 rounded-full animate-spin mb-3" style={{ animationDuration: '0.6s' }}></div>
            <div className="text-white/70 text-sm font-medium">Loading chart...</div>
          </div>
        </div>
      )}
      
      <div style={{ opacity: isLoading ? 0 : 1, transition: 'opacity 0.3s ease' }}>
        <SimpleTradingViewChart 
          {...props}
          onError={handleIframeError}
          onLoad={handleIframeLoad}
        />
      </div>
    </div>
  );
});

RobustTradingViewChart.displayName = 'RobustTradingViewChart';

export default RobustTradingViewChart;
