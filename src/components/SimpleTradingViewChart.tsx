import React, { memo } from 'react';

interface SimpleTradingViewChartProps {
  symbol: string;
  interval?: string;
  theme?: 'light' | 'dark';
  width?: string | number;
  height?: string | number;
  timezone?: string;
  range?: string;
  startDate?: string;
  endDate?: string;
  onError?: () => void;
  onLoad?: () => void;
}

const SimpleTradingViewChart: React.FC<SimpleTradingViewChartProps> = memo(({
  symbol,
  interval = 'D',
  theme = 'dark',
  width = '100%',
  height = 500,
  timezone = 'Etc/UTC',
  range = '3M',
  startDate,
  endDate,
  onError,
  onLoad
}) => {
  // Calculate precise date range for trade focus
  let calculatedRange = range;
  let fromDate = '';
  let toDate = '';

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Add context around the trade (20% padding on each side, minimum 7 days)
    const contextDays = Math.max(Math.ceil(diffDays * 0.4), 7);

    // Calculate from and to dates with context
    const fromDateTime = new Date(start.getTime() - (contextDays * 24 * 60 * 60 * 1000));
    const toDateTime = new Date(end.getTime() + (contextDays * 24 * 60 * 60 * 1000));

    // Format dates for TradingView (YYYY-MM-DD)
    fromDate = fromDateTime.toISOString().split('T')[0];
    toDate = toDateTime.toISOString().split('T')[0];

    // Use custom range for precise control
    calculatedRange = 'ALL';
  } else if (startDate) {
    // For open trades, show from entry date to now with context
    const start = new Date(startDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    const contextDays = Math.max(Math.ceil(diffDays * 0.2), 7);
    const fromDateTime = new Date(start.getTime() - (contextDays * 24 * 60 * 60 * 1000));

    fromDate = fromDateTime.toISOString().split('T')[0];
    toDate = now.toISOString().split('T')[0];
    calculatedRange = 'ALL';
  }

  // Format symbol for TradingView
  const formattedSymbol = symbol.includes(':') ? symbol : `NASDAQ:${symbol}`;

  // Build TradingView URL
  const tradingViewUrl = new URL('https://www.tradingview.com/widgetembed/');

  // Add parameters
  const params: Record<string, string> = {
    frameElementId: 'tradingview_chart',
    symbol: formattedSymbol,
    interval: interval,
    hidesidetoolbar: '1',
    symboledit: '0',
    saveimage: '0',
    toolbarbg: theme === 'dark' ? '131722' : 'f1f3f6',
    studies: 'RSI@tv-basicstudies,MACD@tv-basicstudies',
    theme: theme,
    style: '1',
    timezone: timezone,
    studies_overrides: '{}',
    overrides: '{}',
    enabled_features: '[]',
    disabled_features: '[]',
    locale: 'en',
    utm_source: '',
    utm_medium: '',
    utm_campaign: '',
    utm_term: '',
    utm_content: ''
  };

  // Add range and date parameters for precise focusing
  if (fromDate && toDate) {
    params.range = calculatedRange;
    params.from = fromDate;
    params.to = toDate;
  } else if (calculatedRange) {
    params.range = calculatedRange;
  }

  // Build query string
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      tradingViewUrl.searchParams.append(key, value.toString());
    }
  });

  return (
    <div 
      className="tradingview-widget-container"
      style={{ 
        width: typeof width === 'string' ? width : `${width}px`,
        height: typeof height === 'string' ? height : `${height}px`,
        minHeight: '400px',
        backgroundColor: theme === 'dark' ? '#0A0A0C' : '#ffffff',
        border: theme === 'dark' ? '1px solid #1A1A1C' : '1px solid #e5e7eb',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
    >
      <iframe
        id="tradingview_chart"
        src={tradingViewUrl.toString()}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          margin: '0',
          padding: '0'
        }}
        frameBorder="0"
        allowTransparency={true}
        scrolling="no"
        allowFullScreen={true}
        title={`TradingView Chart for ${symbol}`}
        onLoad={onLoad}
        onError={onError}
      />
      
      {/* Trade annotations overlay */}
      <div
        className="absolute top-2 left-2 z-10 bg-black/80 text-white text-xs px-2 py-1 rounded"
        style={{
          position: 'absolute',
          top: '8px',
          left: '8px',
          zIndex: 10,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          fontSize: '12px',
          padding: '4px 8px',
          borderRadius: '4px',
          pointerEvents: 'none'
        }}
      >
        {symbol} • {interval} • Trade Focus
      </div>

      {/* Trade markers overlay */}
      {(startDate || endDate) && (
        <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 5 }}>
          {/* Entry marker */}
          {startDate && (
            <div
              className="absolute flex items-center"
              style={{
                top: '20%',
                right: '20px',
                zIndex: 15
              }}
            >
              <div className="bg-emerald-500/90 text-white text-xs px-2 py-1 rounded-l flex items-center">
                <div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
                ENTRY
              </div>
              <div className="bg-emerald-500/90 text-white text-xs px-2 py-1 rounded-r">
                {new Date(startDate).toLocaleDateString()}
              </div>
            </div>
          )}

          {/* Exit marker */}
          {endDate && (
            <div
              className="absolute flex items-center"
              style={{
                top: '30%',
                right: '20px',
                zIndex: 15
              }}
            >
              <div className="bg-red-500/90 text-white text-xs px-2 py-1 rounded-l flex items-center">
                <div className="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
                EXIT
              </div>
              <div className="bg-red-500/90 text-white text-xs px-2 py-1 rounded-r">
                {new Date(endDate).toLocaleDateString()}
              </div>
            </div>
          )}

          {/* Trade duration indicator */}
          {startDate && endDate && (
            <div
              className="absolute"
              style={{
                bottom: '20px',
                right: '20px',
                zIndex: 15
              }}
            >
              <div className="bg-blue-500/90 text-white text-xs px-2 py-1 rounded flex items-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                {(() => {
                  const start = new Date(startDate);
                  const end = new Date(endDate);
                  const diffTime = Math.abs(end.getTime() - start.getTime());
                  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                  return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
                })()}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
});

SimpleTradingViewChart.displayName = 'SimpleTradingViewChart';

export default SimpleTradingViewChart;
