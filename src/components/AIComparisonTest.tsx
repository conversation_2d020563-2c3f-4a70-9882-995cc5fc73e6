import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, Sparkles } from 'lucide-react';
import SocialMediaChartGenerator, { ChartConfig } from './SocialMediaChartGenerator';
import { generateChartDataWithAI } from '@/services/aiChartDataService';
import { useAuth } from '@/contexts/AuthContext';

const AIComparisonTest: React.FC = () => {
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chartConfig, setChartConfig] = useState<ChartConfig | null>(null);
  const [reasoning, setReasoning] = useState<string>('');
  const { user } = useAuth();

  const comparisonQueries = [
    "Apple vs Microsoft revenue comparison",
    "Tesla vs Ford quarterly sales",
    "Netflix vs Disney subscriber growth",
    "Compare Amazon and Google revenue",
    "iPhone vs Samsung market share",
    "Bitcoin vs Ethereum price comparison"
  ];

  const generateChart = async (testQuery?: string) => {
    const queryToUse = testQuery || query;
    if (!queryToUse.trim()) return;

    setLoading(true);
    setError(null);
    setChartConfig(null);
    setReasoning('');

    try {
      const response = await generateChartDataWithAI({
        query: queryToUse,
        userId: user?.id
      });

      if (response.success && response.chartConfig) {
        setChartConfig(response.chartConfig);
        setReasoning(response.reasoning || '');
      } else {
        setError(response.error || 'Failed to generate chart');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate chart');
    } finally {
      setLoading(false);
    }
  };

  const handleSampleQuery = (sampleQuery: string) => {
    setQuery(sampleQuery);
    generateChart(sampleQuery);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">AI Comparison Chart Test</h1>
        <p className="text-gray-600">
          Test the AI's ability to detect comparison requests and generate comparison charts
        </p>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Test Comparison Queries</h3>
        
        <div className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Enter your comparison query..."
              onKeyPress={(e) => e.key === 'Enter' && generateChart()}
            />
            <Button
              onClick={() => generateChart()}
              disabled={loading || !query.trim()}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generate
                </>
              )}
            </Button>
          </div>

          <div>
            <p className="text-sm font-medium mb-2">Sample Comparison Queries:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {comparisonQueries.map((sampleQuery, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSampleQuery(sampleQuery)}
                  disabled={loading}
                  className="text-left justify-start"
                >
                  {sampleQuery}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {error && (
        <Card className="p-6 border-red-200 bg-red-50">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
          <p className="text-red-600">{error}</p>
        </Card>
      )}

      {reasoning && (
        <Card className="p-6 bg-blue-50 border-blue-200">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">AI Reasoning</h3>
          <p className="text-blue-700">{reasoning}</p>
        </Card>
      )}

      {chartConfig && (
        <Card className="p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold">Generated Chart</h3>
            <div className="text-sm text-gray-600 mt-1">
              <strong>Chart Type:</strong> {chartConfig.type}
              {chartConfig.type === 'comparison-bar' && (
                <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                  ✓ Comparison Detected
                </span>
              )}
            </div>
            {chartConfig.legendData && (
              <div className="text-sm text-gray-600 mt-1">
                <strong>Series:</strong> {chartConfig.legendData.join(', ')}
              </div>
            )}
          </div>
          
          <SocialMediaChartGenerator 
            config={chartConfig}
            onExport={(imageData) => {
              console.log('Chart exported:', imageData);
            }}
          />
        </Card>
      )}

      <Card className="p-6 bg-gray-50">
        <h3 className="text-lg font-semibold mb-4">Expected Behavior</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p><strong>✓ Comparison Keywords:</strong> When you use words like "vs", "versus", "compare", "comparison", "against", the AI should detect this and use the "comparison-bar" chart type.</p>
          <p><strong>✓ Multiple Data Series:</strong> The AI should generate data as an array of arrays for comparison charts.</p>
          <p><strong>✓ Legend Support:</strong> Comparison charts should include legendData and showLegend: true.</p>
          <p><strong>✓ Overlapping Bars:</strong> The chart should use barGap: "-30%" or similar for overlapping effect.</p>
          <p><strong>✓ Contrasting Colors:</strong> Different colors should be used for each series being compared.</p>
        </div>
      </Card>
    </div>
  );
};

export default AIComparisonTest;
