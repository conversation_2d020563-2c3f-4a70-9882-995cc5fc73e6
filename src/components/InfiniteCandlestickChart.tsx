import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { format } from 'date-fns';
import { BarChart3 } from 'lucide-react';
import { InfiniteChartManager } from '@/services/infiniteChartService';
import { FocusedOHLCVData } from '@/services/tradeAnalysisService';
import {
  ChartInteractionState,
  initializeInfiniteChartState,
  handleSmoothZoom,
  handlePanStart,
  handleSmoothPan,
  handleSmoothPanEnd,
  dataToCanvas,
  calculatePriceRange,
  debounce,
  throttle,
  hasSignificantViewportChange
} from '@/utils/chartInteractions';

interface TradeMarker {
  timestamp: number;
  price: number;
  type: 'entry' | 'exit';
  direction?: 'LONG' | 'SHORT';
  label: string;
  color: string;
}

interface InfiniteCandlestickChartProps {
  symbol: string;
  tradeTimestamp: number;
  entryPrice: number;
  exitPrice?: number;
  tradeMarkers: TradeMarker[];
  height?: number;
  className?: string;
}

const InfiniteCandlestickChart: React.FC<InfiniteCandlestickChartProps> = ({
  symbol,
  tradeTimestamp,
  entryPrice,
  exitPrice,
  tradeMarkers,
  height = 500,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const chartManagerRef = useRef<InfiniteChartManager | null>(null);
  
  const [chartState, setChartState] = useState<ChartInteractionState | null>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height });
  const [visibleData, setVisibleData] = useState<FocusedOHLCVData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredCandle, setHoveredCandle] = useState<FocusedOHLCVData | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);

  // Initial time range (24 hours in milliseconds)
  const initialTimeRange = 24 * 60 * 60 * 1000;

  // Initialize chart manager and state
  useEffect(() => {
    const onDataUpdate = (data: any) => {
      // Trigger re-render when new data is loaded
      loadVisibleData();
    };

    chartManagerRef.current = new InfiniteChartManager(
      symbol,
      tradeTimestamp,
      entryPrice,
      onDataUpdate
    );

    // Initialize chart state centered on trade
    const initialState = initializeInfiniteChartState(
      tradeTimestamp,
      initialTimeRange,
      entryPrice * 0.95, // 5% below entry price
      entryPrice * 1.05  // 5% above entry price
    );

    setChartState(initialState);

    // Load initial data
    loadVisibleData(initialState);

    return () => {
      chartManagerRef.current = null;
    };
  }, [symbol, tradeTimestamp, entryPrice]);

  // Handle canvas resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height });
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [height]);

  // Auto-adjust height for current viewport
  const autoAdjustHeight = useCallback((newState: ChartInteractionState) => {
    if (visibleData.length === 0) return;

    const viewportData = visibleData.filter(d =>
      d.timestamp >= newState.viewport.xMin &&
      d.timestamp <= newState.viewport.xMax
    );

    if (viewportData.length > 0) {
      const priceRange = calculatePriceRange(viewportData);
      // NO padding - perfect fit with highest/lowest candles touching chart edges

      setChartState(prev => prev ? {
        ...prev,
        viewport: {
          ...prev.viewport,
          yMin: priceRange.min,
          yMax: priceRange.max
        }
      } : null);
    }
  }, [visibleData]);

  // Debounced data loading function
  const debouncedLoadData = useMemo(
    () => debounce((state: ChartInteractionState) => {
      loadVisibleData(state);
    }, 150),
    []
  );

  // Throttled viewport change handler
  const throttledViewportChange = useMemo(
    () => throttle((newState: ChartInteractionState) => {
      if (chartState && hasSignificantViewportChange(chartState.viewport, newState.viewport)) {
        debouncedLoadData(newState);
        // Auto-adjust height when viewport changes significantly
        autoAdjustHeight(newState);
      }
    }, 50),
    [chartState, debouncedLoadData, autoAdjustHeight]
  );

  // Handle wheel events properly to avoid passive listener issues
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (chartState) {
        const rect = canvas.getBoundingClientRect();
        const newState = handleSmoothZoom(chartState, e, rect, initialTimeRange);
        setChartState(newState);
        throttledViewportChange(newState);
      }
    };

    canvas.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
    };
  }, [chartState, initialTimeRange, throttledViewportChange]);

  // Load visible data for current viewport with unlimited range support
  const loadVisibleData = useCallback(async (state?: ChartInteractionState) => {
    if (!chartManagerRef.current) return;

    const currentState = state || chartState;
    if (!currentState) return;

    setIsLoading(true);

    try {
      // Larger buffer for unlimited scrolling - load much more data ahead
      const viewportRange = currentState.viewport.xMax - currentState.viewport.xMin;
      const bufferTime = viewportRange * 2; // 2x viewport size buffer
      const startTime = currentState.viewport.xMin - bufferTime;
      const endTime = currentState.viewport.xMax + bufferTime;

      console.log(`[InfiniteCandlestickChart] Loading unlimited data range: ${format(new Date(startTime), 'yyyy-MM-dd')} to ${format(new Date(endTime), 'yyyy-MM-dd')}`);

      const data = await chartManagerRef.current.getDataInRange(startTime, endTime);

      console.log(`[InfiniteCandlestickChart] Loaded ${data.length} unlimited data points for ${symbol}`);
      if (data.length > 0) {
        console.log(`[InfiniteCandlestickChart] Sample data:`, data[0]);
        console.log(`[InfiniteCandlestickChart] Full data range: ${format(new Date(data[0].timestamp), 'yyyy-MM-dd HH:mm')} to ${format(new Date(data[data.length - 1].timestamp), 'yyyy-MM-dd HH:mm')}`);
        console.log(`[InfiniteCandlestickChart] Data spans ${Math.round((data[data.length - 1].timestamp - data[0].timestamp) / (1000 * 60 * 60 * 24))} days`);
      }

      setVisibleData(data);

      // Update price range based on viewport data only (not all loaded data)
      const viewportData = data.filter(d =>
        d.timestamp >= currentState.viewport.xMin &&
        d.timestamp <= currentState.viewport.xMax
      );

      if (viewportData.length > 0) {
        const priceRange = calculatePriceRange(viewportData);
        // NO padding - perfect fit with highest/lowest candles touching chart edges

        setChartState(prev => prev ? {
          ...prev,
          viewport: {
            ...prev.viewport,
            yMin: priceRange.min,
            yMax: priceRange.max
          }
        } : null);
      }

      // Aggressive preloading for unlimited scrolling - load weeks of data
      chartManagerRef.current.preloadAroundRange(startTime, endTime, 8); // 8 weeks buffer

    } catch (error) {
      console.error('[InfiniteCandlestickChart] Error loading unlimited data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [chartState, symbol]);



  // Draw the chart
  const drawChart = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || !chartState || visibleData.length === 0) return;

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasSize.width * dpr;
    canvas.height = canvasSize.height * dpr;
    canvas.style.width = `${canvasSize.width}px`;
    canvas.style.height = `${canvasSize.height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.fillStyle = '#0A0A0C';
    ctx.fillRect(0, 0, canvasSize.width, canvasSize.height);

    // Chart margins
    const margin = { top: 20, right: 60, bottom: 40, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;

    // Draw grid
    drawGrid(ctx, margin, chartWidth, chartHeight);

    // Draw candlesticks
    drawCandlesticks(ctx, margin, chartWidth, chartHeight);

    // Draw trade markers
    drawTradeMarkers(ctx, margin, chartWidth, chartHeight);

    // Draw current time line
    drawCurrentTimeLine(ctx, margin, chartWidth, chartHeight);

    // Draw Y-axis labels
    drawYAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Draw X-axis labels
    drawXAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Draw loading indicator if loading
    if (isLoading) {
      drawLoadingIndicator(ctx, margin, chartWidth, chartHeight);
    }

    // Draw tooltip if hovering
    if (hoveredCandle && mousePosition) {
      drawTooltip(ctx, hoveredCandle, mousePosition);
    }
  }, [chartState, visibleData, canvasSize, tradeMarkers, isLoading, hoveredCandle, mousePosition]);

  // Draw grid lines
  const drawGrid = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    ctx.strokeStyle = '#1A1A1C';
    ctx.lineWidth = 1;

    // Horizontal grid lines (price levels)
    const priceStep = (chartState.viewport.yMax - chartState.viewport.yMin) / 8;
    for (let i = 0; i <= 8; i++) {
      const price = chartState.viewport.yMin + i * priceStep;
      const y = margin.top + chartHeight - ((price - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      
      ctx.beginPath();
      ctx.moveTo(margin.left, y);
      ctx.lineTo(margin.left + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines (time)
    const timeStep = (chartState.viewport.xMax - chartState.viewport.xMin) / 8;
    for (let i = 0; i <= 8; i++) {
      const time = chartState.viewport.xMin + i * timeStep;
      const x = margin.left + ((time - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;
      
      ctx.beginPath();
      ctx.moveTo(x, margin.top);
      ctx.lineTo(x, margin.top + chartHeight);
      ctx.stroke();
    }
  };

  // Draw candlesticks with unlimited data optimization
  const drawCandlesticks = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    // Efficient viewport culling for unlimited data
    const viewportStart = chartState.viewport.xMin;
    const viewportEnd = chartState.viewport.xMax;

    // Only render candles that are actually visible in viewport
    const visibleCandles = visibleData.filter(candle =>
      candle.timestamp >= viewportStart &&
      candle.timestamp <= viewportEnd
    );

    if (visibleCandles.length === 0) {
      console.log('[InfiniteCandlestickChart] No visible candles in current viewport');
      return;
    }

    console.log(`[InfiniteCandlestickChart] Rendering ${visibleCandles.length} visible candles out of ${visibleData.length} total loaded`);

    // Calculate optimal candle width for current zoom level
    const timeRange = viewportEnd - viewportStart;
    const avgTimeInterval = visibleCandles.length > 1 ?
      (visibleCandles[visibleCandles.length - 1].timestamp - visibleCandles[0].timestamp) / (visibleCandles.length - 1) :
      15 * 60 * 1000; // 15 minutes default

    const pixelsPerMs = chartWidth / timeRange;
    const candleSpacing = avgTimeInterval * pixelsPerMs;
    const candleWidth = Math.max(0.5, Math.min(candleSpacing * 0.8, 25)); // Allow thinner candles for zoomed out view

    visibleCandles.forEach((candle) => {
      const x = margin.left + ((candle.timestamp - chartState.viewport.xMin) / timeRange) * chartWidth;
      
      const highY = margin.top + chartHeight - ((candle.high - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      const lowY = margin.top + chartHeight - ((candle.low - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      const openY = margin.top + chartHeight - ((candle.open - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      const closeY = margin.top + chartHeight - ((candle.close - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;

      const isGreen = candle.close >= candle.open;
      const color = isGreen ? '#10b981' : '#ef4444';

      // Draw wick
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, highY);
      ctx.lineTo(x, lowY);
      ctx.stroke();

      // Draw body with dynamic minimum height for better zoom visibility
      const bodyTop = Math.min(openY, closeY);
      const priceRange = chartState.viewport.yMax - chartState.viewport.yMin;
      const actualPriceRange = Math.abs(candle.close - candle.open);

      // Dynamic minimum height based on zoom level and candle width
      const baseMinHeight = Math.max(2, Math.round(candleWidth * 0.2));
      const zoomAdjustedMinHeight = Math.max(baseMinHeight, 4); // Minimum 4px when zoomed in
      const bodyHeight = Math.max(Math.abs(closeY - openY), zoomAdjustedMinHeight);

      ctx.fillStyle = color;
      ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
      
      // Add border for definition
      ctx.strokeStyle = color;
      ctx.lineWidth = 0.5;
      ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
    });
  };

  // Draw trade markers
  const drawTradeMarkers = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    tradeMarkers.forEach((marker) => {
      if (marker.timestamp < chartState.viewport.xMin || marker.timestamp > chartState.viewport.xMax) {
        return;
      }

      const x = margin.left + ((marker.timestamp - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;

      // Draw vertical line
      ctx.strokeStyle = marker.color;
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(x, margin.top);
      ctx.lineTo(x, margin.top + chartHeight);
      ctx.stroke();
      ctx.setLineDash([]);

      // Draw marker circle
      ctx.fillStyle = marker.color;
      ctx.beginPath();
      ctx.arc(x, y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Draw label
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px monospace';
      ctx.textAlign = 'center';
      ctx.fillText(marker.label, x, margin.top - 5);
    });
  };

  // Draw current time line to show the boundary
  const drawCurrentTimeLine = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    const currentTime = Date.now();

    // Only draw if current time is visible in viewport
    if (currentTime >= chartState.viewport.xMin && currentTime <= chartState.viewport.xMax) {
      const x = margin.left + ((currentTime - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;

      // Draw current time line
      ctx.strokeStyle = '#fbbf24'; // Yellow color for current time
      ctx.lineWidth = 2;
      ctx.setLineDash([3, 3]);
      ctx.beginPath();
      ctx.moveTo(x, margin.top);
      ctx.lineTo(x, margin.top + chartHeight);
      ctx.stroke();
      ctx.setLineDash([]);

      // Draw "NOW" label
      ctx.fillStyle = '#fbbf24';
      ctx.font = 'bold 12px monospace';
      ctx.textAlign = 'center';
      ctx.fillText('NOW', x, margin.top - 5);
    }
  };

  // Draw Y-axis price labels
  const drawYAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    ctx.fillStyle = '#666';
    ctx.font = '12px monospace';
    ctx.textAlign = 'left';

    const priceStep = (chartState.viewport.yMax - chartState.viewport.yMin) / 8;
    for (let i = 0; i <= 8; i++) {
      const price = chartState.viewport.yMin + i * priceStep;
      const y = margin.top + chartHeight - ((price - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;

      ctx.fillText(`$${price.toFixed(2)}`, margin.left + chartWidth + 5, y + 4);
    }
  };

  // Draw X-axis time labels
  const drawXAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    ctx.fillStyle = '#666';
    ctx.font = '12px monospace';
    ctx.textAlign = 'center';

    const timeStep = (chartState.viewport.xMax - chartState.viewport.xMin) / 8;
    for (let i = 0; i <= 8; i++) {
      const time = chartState.viewport.xMin + i * timeStep;
      const x = margin.left + ((time - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;

      const timeLabel = format(new Date(time), 'MMM dd HH:mm');
      ctx.fillText(timeLabel, x, margin.top + chartHeight + 20);
    }
  };

  // Draw loading indicator
  const drawLoadingIndicator = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(margin.left, margin.top, chartWidth, chartHeight);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = '14px monospace';
    ctx.textAlign = 'center';
    ctx.fillText('Loading...', margin.left + chartWidth / 2, margin.top + chartHeight / 2);
  };

  // Draw tooltip
  const drawTooltip = (
    ctx: CanvasRenderingContext2D,
    candle: FocusedOHLCVData,
    position: { x: number; y: number }
  ) => {
    const tooltipWidth = 200;
    const tooltipHeight = 100;
    let x = position.x + 10;
    let y = position.y - tooltipHeight - 10;

    if (x + tooltipWidth > canvasSize.width) x = position.x - tooltipWidth - 10;
    if (y < 0) y = position.y + 10;

    ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
    ctx.fillRect(x, y, tooltipWidth, tooltipHeight);
    ctx.strokeStyle = '#333';
    ctx.strokeRect(x, y, tooltipWidth, tooltipHeight);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = '12px monospace';
    ctx.textAlign = 'left';

    const padding = 10;
    ctx.fillText(`Time: ${format(new Date(candle.timestamp), 'MMM dd HH:mm')}`, x + padding, y + padding + 15);
    ctx.fillText(`Open: $${candle.open.toFixed(2)}`, x + padding, y + padding + 30);
    ctx.fillText(`High: $${candle.high.toFixed(2)}`, x + padding, y + padding + 45);
    ctx.fillText(`Low: $${candle.low.toFixed(2)}`, x + padding, y + padding + 60);
    ctx.fillText(`Close: $${candle.close.toFixed(2)}`, x + padding, y + padding + 75);
  };

  // Redraw chart when state changes
  useEffect(() => {
    drawChart();
  }, [drawChart]);

  // Show loading state if no data
  if (!chartState || visibleData.length === 0) {
    return (
      <div className={`w-full bg-[#0A0A0C] rounded-lg border border-[#1A1A1C] ${className}`}>
        <div className="p-4 border-b border-[#1A1A1C]">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-white text-lg font-semibold">{symbol}</h3>
              <p className="text-white/60 text-sm">Infinite Trade Analysis - 15-Minute Chart</p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center h-[400px]">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-white/20 mx-auto mb-3 animate-pulse" />
            <p className="text-white/60 text-sm">Loading infinite chart data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`w-full bg-[#0A0A0C] rounded-lg border border-[#1A1A1C] ${className}`}>
      {/* Chart Header */}
      <div className="p-4 border-b border-[#1A1A1C]">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-white text-lg font-semibold">{symbol}</h3>
            <p className="text-white/60 text-sm">Unlimited Trade Analysis - 15-Minute Chart</p>
            <p className="text-white/40 text-xs mt-1">
              Viewport: {format(new Date(chartState.viewport.xMin), 'MMM dd, HH:mm')} - {format(new Date(chartState.viewport.xMax), 'MMM dd, HH:mm')}
            </p>
            <p className="text-white/40 text-xs">
              Loaded: {visibleData.length} candles
              {visibleData.length > 0 && (
                <span> • Spans {Math.round((visibleData[visibleData.length - 1].timestamp - visibleData[0].timestamp) / (1000 * 60 * 60 * 24))} days</span>
              )}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-xs text-white/50">
              <div>Zoom: {chartState.viewport.zoomLevel.toFixed(1)}x • Centered on Trade</div>
              <div className="text-white/40">∞ Unlimited Data • Perfect Fit (No Gaps)</div>
              <div className="text-white/30">Scroll to zoom in/out from trade • Drag to pan</div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  if (chartState) {
                    // Center on trade with current zoom level
                    const currentRange = chartState.viewport.xMax - chartState.viewport.xMin;
                    const currentTime = Date.now();
                    const newXMin = tradeTimestamp - currentRange / 2;
                    const newXMax = Math.min(tradeTimestamp + currentRange / 2, currentTime);

                    const centeredState = {
                      ...chartState,
                      viewport: {
                        ...chartState.viewport,
                        xMin: newXMin,
                        xMax: newXMax
                      }
                    };
                    setChartState(centeredState);
                    autoAdjustHeight(centeredState);
                  }
                }}
                className="px-3 py-1 text-xs bg-green-500/20 text-green-300 border border-green-500/30 rounded hover:bg-green-500/30 transition-colors"
              >
                📍 Center on Trade
              </button>
              <button
                onClick={() => {
                  if (chartState) {
                    const resetState = initializeInfiniteChartState(
                      tradeTimestamp,
                      initialTimeRange,
                      entryPrice * 0.95,
                      entryPrice * 1.05
                    );
                    setChartState(resetState);
                    loadVisibleData(resetState);
                  }
                }}
                className="px-3 py-1 text-xs bg-blue-500/20 text-blue-300 border border-blue-500/30 rounded hover:bg-blue-500/30 transition-colors"
              >
                🔄 Reset View
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Chart Canvas */}
      <div className="relative">
        <canvas
          ref={canvasRef}
          className={`touch-none ${chartState?.isDragging ? 'cursor-grabbing' : 'cursor-crosshair'}`}
          onMouseDown={(e) => {
            if (chartState && canvasRef.current) {
              const rect = canvasRef.current.getBoundingClientRect();
              setChartState(handlePanStart(chartState, e.nativeEvent, rect));
            }
          }}
          onMouseMove={(e) => {
            if (chartState && canvasRef.current) {
              const rect = canvasRef.current.getBoundingClientRect();

              if (chartState.isDragging) {
                const newState = handleSmoothPan(chartState, e.nativeEvent, rect);
                setChartState(newState);
                throttledViewportChange(newState);
              } else {
                // Handle hover for tooltip
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                setMousePosition({ x: mouseX, y: mouseY });

                // Find closest candle for tooltip
                const margin = { top: 20, right: 60, bottom: 40, left: 20 };
                const chartWidth = canvasSize.width - margin.left - margin.right;

                if (mouseX >= margin.left && mouseX <= margin.left + chartWidth) {
                  const dataX = chartState.viewport.xMin + ((mouseX - margin.left) / chartWidth) * (chartState.viewport.xMax - chartState.viewport.xMin);

                  let closestCandle = null;
                  let minDistance = Infinity;

                  visibleData.forEach((candle) => {
                    const distance = Math.abs(candle.timestamp - dataX);
                    if (distance < minDistance) {
                      minDistance = distance;
                      closestCandle = candle;
                    }
                  });

                  setHoveredCandle(closestCandle);
                } else {
                  setHoveredCandle(null);
                }
              }
            }
          }}
          onMouseUp={() => {
            if (chartState) {
              setChartState(handleSmoothPanEnd(chartState));
            }
          }}
          onMouseLeave={() => {
            if (chartState) {
              setChartState(handleSmoothPanEnd(chartState));
            }
            setHoveredCandle(null);
            setMousePosition(null);
          }}
        />

        {/* Chart status indicators */}
        <div className="absolute bottom-2 left-2 space-y-1">
          <div className="bg-black/80 text-white/70 text-xs px-2 py-1 rounded">
            ∞ Unlimited Chart • Perfect Fit
          </div>
          {visibleData.length > 0 && (
            <div className={`text-xs px-2 py-1 rounded ${
              visibleData[0].date.includes(':')
                ? 'bg-green-500/20 text-green-300'
                : 'bg-yellow-500/20 text-yellow-300'
            }`}>
              {visibleData[0].date.includes(':') ? '📊 Real Polygon Data' : '🔧 Mock Data'}
            </div>
          )}
          <div className="bg-purple-500/20 text-purple-300 text-xs px-2 py-1 rounded">
            📏 Zero Gaps • Full Height Usage
          </div>
          {visibleData.length > 1000 && (
            <div className="bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded">
              🚀 {Math.round(visibleData.length / 100) / 10}K+ Candles Loaded
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InfiniteCandlestickChart;
