import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Trophy, Users, Globe, Crown, Info, Plus } from 'lucide-react';
import { 
  checkWhopCompetitionPermissions, 
  createWhopCompetition,
  validateCompetitionScope,
  WhopCompetitionPermissions 
} from '@/services/whopCompetitionService';
import { 
  CreateWhopCompetitionRequest, 
  COMPETITION_SCOPE_OPTIONS,
  CompetitionScopeOption 
} from '@/types/whopCompetition';

interface WhopCompetitionCreatorProps {
  onCompetitionCreated?: () => void;
}

const WhopCompetitionCreator: React.FC<WhopCompetitionCreatorProps> = ({
  onCompetitionCreated
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [permissions, setPermissions] = useState<WhopCompetitionPermissions | null>(null);
  const [loadingPermissions, setLoadingPermissions] = useState(true);

  // Check if we're in localhost development environment
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  const [formData, setFormData] = useState<CreateWhopCompetitionRequest>({
    name: '',
    description: '',
    starting_balance: 100000,
    max_participants: undefined,
    entry_fee: 0,
    prize_pool: 0,
    competition_start: '',
    competition_end: '',
    registration_start: '',
    registration_end: '',
    rules: {},
    allowed_securities: undefined,
    position_limits: {},
    competition_scope: 'public'
  });

  const [advancedSettings, setAdvancedSettings] = useState({
    hasRegistrationPeriod: false,
    hasParticipantLimit: false,
    hasEntryFee: false,
    hasPrizePool: false,
    hasSecurityRestrictions: false,
    hasPositionLimits: false
  });

  // Load permissions on component mount
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        const perms = await checkWhopCompetitionPermissions();
        setPermissions(perms);
      } catch (error) {
        console.error('Error loading permissions:', error);
      } finally {
        setLoadingPermissions(false);
      }
    };

    loadPermissions();
  }, []);

  const handleInputChange = (field: keyof CreateWhopCompetitionRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getAvailableScopeOptions = (): CompetitionScopeOption[] => {
    if (!permissions) return [];

    return COMPETITION_SCOPE_OPTIONS.filter(option => {
      if (option.requiresOfficialOsis && !permissions.isOfficialOsis) {
        return false;
      }
      if (option.requiresWhopOwner && !permissions.isWhopOwner) {
        return false;
      }
      return true;
    });
  };

  const getScopeIcon = (scope: string) => {
    switch (scope) {
      case 'public': return <Globe className="w-4 h-4" />;
      case 'whop_local': return <Users className="w-4 h-4" />;
      case 'whop_cross_community': return <Crown className="w-4 h-4" />;
      default: return <Trophy className="w-4 h-4" />;
    }
  };

  const getScopeBadgeColor = (scope: string) => {
    switch (scope) {
      case 'public': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'whop_local': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'whop_cross_community': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!permissions) {
        throw new Error('Permissions not loaded');
      }

      // Validate scope permissions
      const validation = validateCompetitionScope(formData.competition_scope, permissions);
      if (!validation.isValid) {
        throw new Error(validation.errorMessage);
      }

      // Clean up optional fields
      const cleanedData: CreateWhopCompetitionRequest = {
        ...formData,
        max_participants: advancedSettings.hasParticipantLimit ? formData.max_participants : undefined,
        entry_fee: advancedSettings.hasEntryFee ? formData.entry_fee : 0,
        prize_pool: advancedSettings.hasPrizePool ? formData.prize_pool : 0,
        registration_start: advancedSettings.hasRegistrationPeriod ? formData.registration_start : undefined,
        registration_end: advancedSettings.hasRegistrationPeriod ? formData.registration_end : undefined,
        allowed_securities: advancedSettings.hasSecurityRestrictions ? formData.allowed_securities : undefined,
        position_limits: advancedSettings.hasPositionLimits ? formData.position_limits : undefined
      };

      await createWhopCompetition(cleanedData);
      
      toast({
        title: "Competition Created",
        description: "Your trading competition has been created successfully!",
      });

      // Reset form
      setFormData({
        name: '',
        description: '',
        starting_balance: 100000,
        max_participants: undefined,
        entry_fee: 0,
        prize_pool: 0,
        competition_start: '',
        competition_end: '',
        registration_start: '',
        registration_end: '',
        rules: {},
        allowed_securities: undefined,
        position_limits: {},
        competition_scope: 'public'
      });
      
      setIsOpen(false);
      onCompetitionCreated?.();
      
    } catch (error: any) {
      console.error('Error creating competition:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create competition",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Don't render if not in localhost development
  if (!isLocalhost) {
    return null;
  }

  if (loadingPermissions) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400"></div>
      </div>
    );
  }

  const availableScopes = getAvailableScopeOptions();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="bg-green-600 hover:bg-green-700 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Create Competition
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Create Trading Competition
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Create a new paper trading competition with custom rules and settings.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Competition Scope Selection */}
          <div className="space-y-3">
            <Label className="text-white">Competition Scope</Label>
            <Select
              value={formData.competition_scope}
              onValueChange={(value) => handleInputChange('competition_scope', value)}
            >
              <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                <SelectValue placeholder="Select competition scope" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {availableScopes.map((option) => (
                  <SelectItem key={option.value} value={option.value} className="text-white">
                    <div className="flex items-center gap-2">
                      <span>{option.icon}</span>
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Scope Description */}
            {formData.competition_scope && (
              <div className="flex items-start gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                {getScopeIcon(formData.competition_scope)}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge className={getScopeBadgeColor(formData.competition_scope)}>
                      {COMPETITION_SCOPE_OPTIONS.find(o => o.value === formData.competition_scope)?.label}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-400">
                    {COMPETITION_SCOPE_OPTIONS.find(o => o.value === formData.competition_scope)?.description}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Permission Info */}
          {permissions && (
            <Alert className="bg-blue-500/10 border-blue-500/30">
              <Info className="h-4 w-4" />
              <AlertDescription className="text-blue-400">
                <div className="space-y-1">
                  <div>Your permissions:</div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {permissions.isWhopOwner && (
                      <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                        Whop Owner
                      </Badge>
                    )}
                    {permissions.isOfficialOsis && (
                      <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                        Official Osis
                      </Badge>
                    )}
                    {!permissions.isWhopOwner && (
                      <Badge className="bg-gray-500/20 text-gray-400 border-gray-500/30">
                        Standard User
                      </Badge>
                    )}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name" className="text-white">Competition Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="bg-gray-800 border-gray-700 text-white"
                placeholder="Enter competition name"
                required
              />
            </div>

            <div>
              <Label htmlFor="description" className="text-white">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="bg-gray-800 border-gray-700 text-white"
                placeholder="Describe your competition..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="starting_balance" className="text-white">Starting Balance ($) *</Label>
              <Input
                id="starting_balance"
                type="number"
                value={formData.starting_balance}
                onChange={(e) => handleInputChange('starting_balance', parseFloat(e.target.value))}
                className="bg-gray-800 border-gray-700 text-white"
                min="1000"
                step="1000"
                required
              />
            </div>
          </div>

          {/* Competition Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="competition_start" className="text-white">Start Date *</Label>
              <Input
                id="competition_start"
                type="datetime-local"
                value={formData.competition_start}
                onChange={(e) => handleInputChange('competition_start', e.target.value)}
                className="bg-gray-800 border-gray-700 text-white"
                required
              />
            </div>
            <div>
              <Label htmlFor="competition_end" className="text-white">End Date *</Label>
              <Input
                id="competition_end"
                type="datetime-local"
                value={formData.competition_end}
                onChange={(e) => handleInputChange('competition_end', e.target.value)}
                className="bg-gray-800 border-gray-700 text-white"
                required
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="border-gray-700 text-gray-300 hover:bg-gray-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {loading ? 'Creating...' : 'Create Competition'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default WhopCompetitionCreator;
