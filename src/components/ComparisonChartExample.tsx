import React from 'react';
import SocialMediaChartGenerator, { ChartConfig, ChartDataPoint } from './SocialMediaChartGenerator';

/**
 * Example component showing how to create a comparison bar chart
 * with overlapping bars of different colors
 */
const ComparisonChartExample: React.FC = () => {
  // Sample data for two companies
  const company1Data: ChartDataPoint[] = [
    { name: 'Q1', value: 1200 },
    { name: 'Q2', value: 1350 },
    { name: 'Q3', value: 1500 },
    { name: 'Q4', value: 1650 }
  ];

  const company2Data: ChartDataPoint[] = [
    { name: 'Q1', value: 980 },
    { name: 'Q2', value: 1100 },
    { name: 'Q3', value: 1250 },
    { name: 'Q4', value: 1400 }
  ];

  // Configuration for comparison chart
  const comparisonConfig: ChartConfig = {
    title: 'Revenue Comparison - Company A vs Company B',
    type: 'comparison-bar',
    data: [company1Data, company2Data], // Array of data series
    colors: ['#3B82F6', '#EF4444'], // Blue and Red
    showValues: true,
    showLegend: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    width: 800,
    height: 600,
    legendData: ['Company A', 'Company B'],
    valueFormatter: (value: number) => `$${value}M`,
    brandText: 'Made with Osis',
    // Comparison-specific options
    barWidth: '60%',
    barGap: '-30%', // Negative value creates overlap
    categoryGap: '20%'
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Comparison Bar Chart Example</h2>
      <p className="text-gray-600 mb-6">
        This example shows how to create overlapping bar charts for comparing data between different series.
      </p>
      
      <SocialMediaChartGenerator 
        config={comparisonConfig}
        onExport={(imageData) => {
          console.log('Chart exported:', imageData);
        }}
      />

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">Key Features:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
          <li><strong>Overlapping Bars:</strong> Use negative barGap values (e.g., '-30%') to create overlapping effect</li>
          <li><strong>Multiple Colors:</strong> Each data series gets its own color from the colors array</li>
          <li><strong>Legend Support:</strong> Automatically displays legend when showLegend is true</li>
          <li><strong>Value Labels:</strong> Shows formatted values on top of each bar</li>
          <li><strong>Interactive Tooltips:</strong> Hover to see detailed information for each series</li>
        </ul>
      </div>
    </div>
  );
};

export default ComparisonChartExample;
