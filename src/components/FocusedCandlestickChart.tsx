import React, { useRef, useEffect, useState, useCallback } from 'react';
import { format } from 'date-fns';
import { BarChart3 } from 'lucide-react';
import { FocusedOHLCVData } from '@/services/tradeAnalysisService';
import {
  ChartInteractionState,
  initializeChartState,
  handleZoom,
  handlePanStart,
  handlePan,
  handlePanEnd,
  resetViewport,
  dataToCanvas
} from '@/utils/chartInteractions';

interface TradeMarker {
  timestamp: number;
  price: number;
  type: 'entry' | 'exit';
  direction?: 'LONG' | 'SHORT';
  label: string;
  color: string;
}

interface FocusedCandlestickChartProps {
  data: FocusedOHLCVData[];
  tradeMarkers: TradeMarker[];
  height?: number;
  symbol: string;
  className?: string;
}

const FocusedCandlestickChart: React.FC<FocusedCandlestickChartProps> = ({
  data,
  tradeMarkers,
  height = 500,
  symbol,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [chartState, setChartState] = useState<ChartInteractionState | null>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height });
  const [hoveredCandle, setHoveredCandle] = useState<FocusedOHLCVData | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);

  // Initialize chart state when data changes
  useEffect(() => {
    if (data.length === 0) return;

    const timestamps = data.map(d => d.timestamp);
    const prices = data.flatMap(d => [d.open, d.high, d.low, d.close]);
    
    const xMin = Math.min(...timestamps);
    const xMax = Math.max(...timestamps);
    const yMin = Math.min(...prices);
    const yMax = Math.max(...prices);

    setChartState(initializeChartState(xMin, xMax, yMin, yMax));
  }, [data]);

  // Handle canvas resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height });
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [height]);

  // Draw the chart
  const drawChart = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || !chartState || data.length === 0) return;

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasSize.width * dpr;
    canvas.height = canvasSize.height * dpr;
    canvas.style.width = `${canvasSize.width}px`;
    canvas.style.height = `${canvasSize.height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.fillStyle = '#0A0A0C';
    ctx.fillRect(0, 0, canvasSize.width, canvasSize.height);

    // Chart margins
    const margin = { top: 20, right: 60, bottom: 40, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;

    // Draw grid
    drawGrid(ctx, margin, chartWidth, chartHeight);

    // Draw candlesticks
    drawCandlesticks(ctx, margin, chartWidth, chartHeight);

    // Draw trade markers
    drawTradeMarkers(ctx, margin, chartWidth, chartHeight);

    // Draw Y-axis labels
    drawYAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Draw X-axis labels
    drawXAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Draw tooltip if hovering
    if (hoveredCandle && mousePosition) {
      drawTooltip(ctx, hoveredCandle, mousePosition);
    }
  }, [chartState, data, canvasSize, tradeMarkers, hoveredCandle, mousePosition]);

  // Draw grid lines
  const drawGrid = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    ctx.strokeStyle = '#1A1A1C';
    ctx.lineWidth = 1;

    // Horizontal grid lines (price levels)
    const priceStep = (chartState.viewport.yMax - chartState.viewport.yMin) / 8;
    for (let i = 0; i <= 8; i++) {
      const price = chartState.viewport.yMin + i * priceStep;
      const y = margin.top + chartHeight - ((price - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      
      ctx.beginPath();
      ctx.moveTo(margin.left, y);
      ctx.lineTo(margin.left + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines (time)
    const timeStep = (chartState.viewport.xMax - chartState.viewport.xMin) / 6;
    for (let i = 0; i <= 6; i++) {
      const time = chartState.viewport.xMin + i * timeStep;
      const x = margin.left + ((time - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;
      
      ctx.beginPath();
      ctx.moveTo(x, margin.top);
      ctx.lineTo(x, margin.top + chartHeight);
      ctx.stroke();
    }
  };

  // Draw candlesticks
  const drawCandlesticks = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState || data.length === 0) return;

    // Calculate visible candles
    const visibleCandles = data.filter(candle =>
      candle.timestamp >= chartState.viewport.xMin &&
      candle.timestamp <= chartState.viewport.xMax
    );

    if (visibleCandles.length === 0) return;

    // Calculate optimal candle width based on visible candles and available space
    const timeRange = chartState.viewport.xMax - chartState.viewport.xMin;
    const avgTimeInterval = visibleCandles.length > 1 ?
      (visibleCandles[visibleCandles.length - 1].timestamp - visibleCandles[0].timestamp) / (visibleCandles.length - 1) :
      15 * 60 * 1000; // 15 minutes in milliseconds

    const pixelsPerMs = chartWidth / timeRange;
    const candleSpacing = avgTimeInterval * pixelsPerMs;
    const candleWidth = Math.max(1, Math.min(candleSpacing * 0.7, 20)); // 70% of spacing, max 20px

    visibleCandles.forEach((candle) => {
      const x = margin.left + ((candle.timestamp - chartState.viewport.xMin) / timeRange) * chartWidth;

      const highY = margin.top + chartHeight - ((candle.high - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      const lowY = margin.top + chartHeight - ((candle.low - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      const openY = margin.top + chartHeight - ((candle.open - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      const closeY = margin.top + chartHeight - ((candle.close - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;

      const isGreen = candle.close >= candle.open;
      const color = isGreen ? '#10b981' : '#ef4444';

      // Draw wick (high-low line) - always draw as thin line
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, highY);
      ctx.lineTo(x, lowY);
      ctx.stroke();

      // Draw body (open-close rectangle) with dynamic minimum height
      const bodyTop = Math.min(openY, closeY);
      const priceRange = chartState.viewport.yMax - chartState.viewport.yMin;
      const actualPriceRange = Math.abs(candle.close - candle.open);

      // Dynamic minimum height based on zoom level and candle width
      const baseMinHeight = Math.max(2, Math.round(candleWidth * 0.2));
      const zoomAdjustedMinHeight = Math.max(baseMinHeight, 4); // Minimum 4px when zoomed in
      const bodyHeight = Math.max(Math.abs(closeY - openY), zoomAdjustedMinHeight);

      // Always fill the body for better visibility
      ctx.fillStyle = color;
      ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);

      // Add border for better definition
      ctx.strokeStyle = color;
      ctx.lineWidth = 0.5;
      ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
    });
  };

  // Draw trade markers
  const drawTradeMarkers = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    tradeMarkers.forEach((marker) => {
      if (marker.timestamp < chartState.viewport.xMin || marker.timestamp > chartState.viewport.xMax) {
        return;
      }

      const x = margin.left + ((marker.timestamp - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;

      // Draw vertical line
      ctx.strokeStyle = marker.color;
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(x, margin.top);
      ctx.lineTo(x, margin.top + chartHeight);
      ctx.stroke();
      ctx.setLineDash([]);

      // Draw marker circle
      ctx.fillStyle = marker.color;
      ctx.beginPath();
      ctx.arc(x, y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Draw label
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '12px monospace';
      ctx.textAlign = 'center';
      ctx.fillText(marker.label, x, margin.top - 5);
    });
  };

  // Draw Y-axis price labels
  const drawYAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    ctx.fillStyle = '#666';
    ctx.font = '12px monospace';
    ctx.textAlign = 'left';

    const priceStep = (chartState.viewport.yMax - chartState.viewport.yMin) / 8;
    for (let i = 0; i <= 8; i++) {
      const price = chartState.viewport.yMin + i * priceStep;
      const y = margin.top + chartHeight - ((price - chartState.viewport.yMin) / (chartState.viewport.yMax - chartState.viewport.yMin)) * chartHeight;
      
      ctx.fillText(`$${price.toFixed(2)}`, margin.left + chartWidth + 5, y + 4);
    }
  };

  // Draw X-axis time labels
  const drawXAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (!chartState) return;

    ctx.fillStyle = '#666';
    ctx.font = '12px monospace';
    ctx.textAlign = 'center';

    const timeStep = (chartState.viewport.xMax - chartState.viewport.xMin) / 6;
    for (let i = 0; i <= 6; i++) {
      const time = chartState.viewport.xMin + i * timeStep;
      const x = margin.left + ((time - chartState.viewport.xMin) / (chartState.viewport.xMax - chartState.viewport.xMin)) * chartWidth;
      
      const timeLabel = format(new Date(time), 'HH:mm');
      ctx.fillText(timeLabel, x, margin.top + chartHeight + 20);
    }
  };

  // Draw tooltip
  const drawTooltip = (
    ctx: CanvasRenderingContext2D,
    candle: FocusedOHLCVData,
    position: { x: number; y: number }
  ) => {
    const tooltipWidth = 200;
    const tooltipHeight = 100;
    let x = position.x + 10;
    let y = position.y - tooltipHeight - 10;

    // Adjust position if tooltip would go off screen
    if (x + tooltipWidth > canvasSize.width) x = position.x - tooltipWidth - 10;
    if (y < 0) y = position.y + 10;

    // Draw tooltip background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
    ctx.fillRect(x, y, tooltipWidth, tooltipHeight);
    ctx.strokeStyle = '#333';
    ctx.strokeRect(x, y, tooltipWidth, tooltipHeight);

    // Draw tooltip content
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '12px monospace';
    ctx.textAlign = 'left';
    
    const padding = 10;
    ctx.fillText(`Time: ${format(new Date(candle.timestamp), 'HH:mm')}`, x + padding, y + padding + 15);
    ctx.fillText(`Open: $${candle.open.toFixed(2)}`, x + padding, y + padding + 30);
    ctx.fillText(`High: $${candle.high.toFixed(2)}`, x + padding, y + padding + 45);
    ctx.fillText(`Low: $${candle.low.toFixed(2)}`, x + padding, y + padding + 60);
    ctx.fillText(`Close: $${candle.close.toFixed(2)}`, x + padding, y + padding + 75);
  };

  // Redraw chart when state changes
  useEffect(() => {
    drawChart();
  }, [drawChart]);

  // Show loading or empty state
  if (data.length === 0) {
    return (
      <div className={`w-full bg-[#0A0A0C] rounded-lg border border-[#1A1A1C] ${className}`}>
        <div className="p-4 border-b border-[#1A1A1C]">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-white text-lg font-semibold">{symbol}</h3>
              <p className="text-white/60 text-sm">Focused Trade Analysis - Hourly Chart</p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center h-[400px]">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-white/20 mx-auto mb-3" />
            <p className="text-white/60 text-sm">No chart data available</p>
            <p className="text-white/40 text-xs">Try adjusting the context hours or check if market data is available for this time period</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`w-full bg-[#0A0A0C] rounded-lg border border-[#1A1A1C] ${className}`}>
      {/* Chart Header */}
      <div className="p-4 border-b border-[#1A1A1C]">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-white text-lg font-semibold">{symbol}</h3>
            <p className="text-white/60 text-sm">Extended Trade Analysis - 15-Minute Chart</p>
            {data.length > 0 && (
              <p className="text-white/40 text-xs mt-1">
                {format(new Date(data[0].timestamp), 'MMM dd, HH:mm')} - {format(new Date(data[data.length - 1].timestamp), 'MMM dd, HH:mm')} ({data.length} candles)
              </p>
            )}
          </div>
          <div className="flex items-center gap-3">
            <div className="text-xs text-white/50">
              <div>Zoom: {chartState?.viewport.zoomLevel ? `${chartState.viewport.zoomLevel.toFixed(1)}x` : '1.0x'}</div>
              <div className="text-white/40">Scroll to zoom • Drag to pan</div>
            </div>
            <button
              onClick={() => {
                if (chartState && data.length > 0) {
                  const timestamps = data.map(d => d.timestamp);
                  const prices = data.flatMap(d => [d.open, d.high, d.low, d.close]);
                  setChartState(resetViewport(
                    chartState,
                    Math.min(...timestamps),
                    Math.max(...timestamps),
                    Math.min(...prices),
                    Math.max(...prices)
                  ));
                }
              }}
              className="px-3 py-1 text-xs bg-blue-500/20 text-blue-300 border border-blue-500/30 rounded hover:bg-blue-500/30 transition-colors"
            >
              Reset View
            </button>
          </div>
        </div>
      </div>

      {/* Chart Canvas */}
      <div className="relative">
        <canvas
          ref={canvasRef}
          className={`touch-none ${chartState?.isDragging ? 'cursor-grabbing' : 'cursor-crosshair'}`}
          onWheel={(e) => {
            if (chartState && canvasRef.current) {
              const rect = canvasRef.current.getBoundingClientRect();
              const timestamps = data.map(d => d.timestamp);
              const prices = data.flatMap(d => [d.open, d.high, d.low, d.close]);
              setChartState(handleZoom(
                chartState,
                e.nativeEvent,
                rect,
                Math.min(...timestamps),
                Math.max(...timestamps),
                Math.min(...prices),
                Math.max(...prices)
              ));
            }
          }}
          onMouseDown={(e) => {
            if (chartState && canvasRef.current) {
              const rect = canvasRef.current.getBoundingClientRect();
              setChartState(handlePanStart(chartState, e.nativeEvent, rect));
            }
          }}
          onMouseMove={(e) => {
            if (chartState && canvasRef.current) {
              const rect = canvasRef.current.getBoundingClientRect();
              
              if (chartState.isDragging) {
                setChartState(handlePan(chartState, e.nativeEvent, rect));
              } else {
                // Handle hover for tooltip
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                setMousePosition({ x: mouseX, y: mouseY });
                
                // Find closest candle for tooltip
                const margin = { top: 20, right: 60, bottom: 40, left: 20 };
                const chartWidth = canvasSize.width - margin.left - margin.right;
                
                if (mouseX >= margin.left && mouseX <= margin.left + chartWidth) {
                  const dataX = chartState.viewport.xMin + ((mouseX - margin.left) / chartWidth) * (chartState.viewport.xMax - chartState.viewport.xMin);
                  
                  let closestCandle = null;
                  let minDistance = Infinity;
                  
                  data.forEach((candle) => {
                    const distance = Math.abs(candle.timestamp - dataX);
                    if (distance < minDistance) {
                      minDistance = distance;
                      closestCandle = candle;
                    }
                  });
                  
                  setHoveredCandle(closestCandle);
                } else {
                  setHoveredCandle(null);
                }
              }
            }
          }}
          onMouseUp={() => {
            if (chartState) {
              setChartState(handlePanEnd(chartState));
            }
          }}
          onMouseLeave={() => {
            if (chartState) {
              setChartState(handlePanEnd(chartState));
            }
            setHoveredCandle(null);
            setMousePosition(null);
          }}
          onTouchStart={(e) => {
            e.preventDefault();
            if (chartState && canvasRef.current && e.touches.length === 1) {
              const rect = canvasRef.current.getBoundingClientRect();
              const touch = e.touches[0];
              const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
              });
              setChartState(handlePanStart(chartState, mouseEvent, rect));
            }
          }}
          onTouchMove={(e) => {
            e.preventDefault();
            if (chartState && canvasRef.current && e.touches.length === 1) {
              const rect = canvasRef.current.getBoundingClientRect();
              const touch = e.touches[0];
              const mouseEvent = new MouseEvent('mousemove', {
                clientX: touch.clientX,
                clientY: touch.clientY
              });
              if (chartState.isDragging) {
                setChartState(handlePan(chartState, mouseEvent, rect));
              }
            }
          }}
          onTouchEnd={(e) => {
            e.preventDefault();
            if (chartState) {
              setChartState(handlePanEnd(chartState));
            }
          }}
        />

        {/* Mobile Instructions */}
        <div className="absolute bottom-2 left-2 lg:hidden">
          <div className="bg-black/80 text-white/70 text-xs px-2 py-1 rounded">
            Drag to pan • Pinch to zoom
          </div>
        </div>
      </div>
    </div>
  );
};

export default FocusedCandlestickChart;
