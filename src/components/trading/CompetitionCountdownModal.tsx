import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Crown,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useWhopAccess } from '@/contexts/WhopContext';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownModalProps {
  isOpen: boolean;
  competitionStartDate: Date;
  onCompetitionStart?: () => void;
}

const CompetitionCountdownModal: React.FC<CompetitionCountdownModalProps> = ({
  isOpen,
  competitionStartDate,
  onCompetitionStart
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [hasStarted, setHasStarted] = useState(false);
  const [communityAnalytics, setCommunityAnalytics] = useState<{
    totalSignups: number;
    totalEarnings: number;
    recentSignups: Array<{
      username: string;
      signupDate: string;
      amount: number;
    }>;
    monthlyStats?: {
      currentMonth: number;
      lastMonth: number;
      growth: number;
    };
  } | null>(null);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);

  const { isAdmin, accessLevel } = useWhopAccess();

  // Owner detection
  const isWhopOwner = React.useMemo(() => {
    // Check: useWhopAccess hook
    return isAdmin || accessLevel === 'admin';

    // Check if we're in a Whop environment and have stored user data indicating admin
    try {
      const storedUserData = localStorage.getItem('whop_user_data');
      if (storedUserData) {
        const userData = JSON.parse(storedUserData);
        if (userData.accessLevel === 'admin' || userData.isAdmin) {
          console.log('📱 Mobile owner detected via stored user data in countdown modal');
          return true;
        }
      }
    } catch (error) {
      console.warn('Error parsing stored user data:', error);
    }

    return false;
  }, [isAdmin, accessLevel]);

  // Load community analytics for Whop owners
  useEffect(() => {
    if (isOpen && isWhopOwner) {
      loadCommunityAnalytics();
    }
  }, [isOpen, isWhopOwner]);

  const loadCommunityAnalytics = async () => {
    if (!isWhopOwner) return;

    setIsLoadingAnalytics(true);
    try {
      const experienceId = new URLSearchParams(window.location.search).get('experienceId') ||
                          window.location.pathname.split('/experiences/')[1];

      if (experienceId) {
        const response = await whopIntermediaryClient.getCommunityAnalytics(experienceId);
        if (response.success && response.data) {
          setCommunityAnalytics(response.data);
        }
      }
    } catch (error) {
      console.error('Failed to load community analytics:', error);
    } finally {
      setIsLoadingAnalytics(false);
    }
  };

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(remaining);

      const now = new Date();
      if (now >= competitionStartDate && !hasStarted) {
        setHasStarted(true);
        if (onCompetitionStart) {
          onCompetitionStart();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate, hasStarted, onCompetitionStart]);

  if (hasStarted) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-[95vw] sm:max-w-5xl lg:max-w-6xl xl:max-w-7xl max-h-[85vh] bg-[#0a0a0a] border border-white/[0.06] text-white focus:outline-none focus:ring-0 shadow-[0_20px_60px_rgba(0,0,0,0.4)]"
        hideCloseButton
        style={{ outline: 'none', boxShadow: '0 20px 60px rgba(0,0,0,0.4)' }}
      >
        <DialogTitle className="sr-only">Trading Competition Countdown</DialogTitle>
        <DialogDescription className="sr-only">
          Countdown timer showing time remaining until the trading competition begins.
        </DialogDescription>
        <div className="p-6 sm:p-8 lg:p-10">
          {/* Top Section - Congrats Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center items-center gap-4 mb-4">
              <img
                src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUyODA4NDczLCJleHAiOjE3ODQzNDQ0NzN9.xMhtKSQzoh0MN4s0Ybi4BQhSToGWQGZCBQblfN8Lnas"
                alt="TradeOff Logo"
                className="w-12 h-12 sm:w-16 sm:h-16 object-contain"
                onError={(e) => {
                  console.error('Logo failed to load:', e);
                  e.currentTarget.style.display = 'none';
                }}
              />
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-normal bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent leading-tight">
                Congrats, and welcome to the biggest competition yet.
              </h1>
            </div>
          </div>

          {/* Countdown Section - Right below header */}
          <div className="text-center mb-8">
            <h3 className="text-lg sm:text-xl font-medium mb-6 text-white/80">Competition starts in:</h3>

            <div className="flex items-center justify-center gap-3">
              <div className="flex flex-col items-center">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-[#0f0f0f] border border-white/[0.06] rounded-lg flex items-center justify-center mb-2 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
                  <motion.span
                    key={timeRemaining.days}
                    initial={{ opacity: 0.6 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0.6 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="text-xl sm:text-2xl font-medium text-white/95"
                  >
                    {timeRemaining.days.toString().padStart(2, '0')}
                  </motion.span>
                </div>
                <span className="text-xs text-white/50 uppercase font-medium tracking-wider">Days</span>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-[#0f0f0f] border border-white/[0.06] rounded-lg flex items-center justify-center mb-2 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
                  <motion.span
                    key={timeRemaining.hours}
                    initial={{ opacity: 0.6 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0.6 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="text-xl sm:text-2xl font-medium text-white/95"
                  >
                    {timeRemaining.hours.toString().padStart(2, '0')}
                  </motion.span>
                </div>
                <span className="text-xs text-white/50 uppercase font-medium tracking-wider">Hours</span>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-[#0f0f0f] border border-white/[0.06] rounded-lg flex items-center justify-center mb-2 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
                  <motion.span
                    key={timeRemaining.minutes}
                    initial={{ opacity: 0.6 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0.6 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="text-xl sm:text-2xl font-medium text-white/95"
                  >
                    {timeRemaining.minutes.toString().padStart(2, '0')}
                  </motion.span>
                </div>
                <span className="text-xs text-white/50 uppercase font-medium tracking-wider">Minutes</span>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-[#0f0f0f] border border-white/[0.06] rounded-lg flex items-center justify-center mb-2 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
                  <motion.span
                    key={timeRemaining.seconds}
                    initial={{ opacity: 0.6 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0.6 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="text-xl sm:text-2xl font-medium text-white/95"
                  >
                    {timeRemaining.seconds.toString().padStart(2, '0')}
                  </motion.span>
                </div>
                <span className="text-xs text-white/50 uppercase font-medium tracking-wider">Seconds</span>
              </div>
            </div>
          </div>

          {/* Bottom Cards Row - Dashboard and Details side by side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">

            {/* Left Card - Owner Analytics (if applicable) */}
            {isWhopOwner && (
              <div className="bg-[#0f0f0f] border border-white/[0.06] rounded-lg p-6 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
                <div className="flex items-center gap-3 mb-5">
                  <Crown className="w-5 h-5 text-white/90" />
                  <h3 className="text-lg font-medium text-white/95">Owner Dashboard</h3>
                </div>

                {isLoadingAnalytics ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white/[0.15] border-t-white/80"></div>
                  </div>
                ) : communityAnalytics ? (
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-[#0a0a0a] border border-white/[0.04] rounded-lg p-4 shadow-[inset_0_1px_0_rgba(255,255,255,0.01)]">
                      <div className="text-white/60 text-xs mb-1 uppercase tracking-wider">Signups</div>
                      <div className="text-white/95 font-medium text-xl">{communityAnalytics.totalSignups}</div>
                    </div>
                    <div className="bg-[#0a0a0a] border border-white/[0.04] rounded-lg p-4 shadow-[inset_0_1px_0_rgba(255,255,255,0.01)]">
                      <div className="text-white/60 text-xs mb-1 uppercase tracking-wider">Earnings</div>
                      <div className="text-white/95 font-medium text-xl">${communityAnalytics.totalEarnings.toFixed(0)}</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-white/60 text-base">Analytics loading...</p>
                  </div>
                )}
              </div>
            )}

            {/* Right Card - Competition Details */}
            <div className="bg-[#0f0f0f] border border-white/[0.06] rounded-lg p-6 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
              <h4 className="text-lg font-medium text-white/95 mb-4">Competition Details</h4>
              <div className="space-y-3 text-white/80">
                <div className="flex items-center gap-3">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                  <span className="text-sm">$100,000 starting balance for all participants</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                  <span className="text-sm">Top #3 Traders win prizes</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                  <span className="text-sm">The competition starts when the countdown is at 0. Goodluck.</span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CompetitionCountdownModal;
