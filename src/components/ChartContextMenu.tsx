import React from 'react';
import { RotateCcw, Trash2, Settings, Grid3X3, Crosshair, ZoomIn, ZoomOut, Maximize2, Info } from 'lucide-react';

interface ChartContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  onClose: () => void;
  onResetView: () => void;
  onFitToData: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onToggleGrid: () => void;
  onToggleCrosshair: () => void;
  onToggleTooltip: () => void;
  onOpenSettings: () => void;
  onClearAllDrawings: () => void;
  showGrid: boolean;
  showCrosshair: boolean;
  showTooltip: boolean;
}

const ChartContextMenu: React.FC<ChartContextMenuProps> = ({
  isOpen,
  position,
  onClose,
  onResetView,
  onFitToData,
  onZoomIn,
  onZoomOut,
  onToggleGrid,
  onToggleCrosshair,
  onToggleTooltip,
  onOpenSettings,
  onClearAllDrawings,
  showGrid,
  showCrosshair,
  showTooltip
}) => {
  if (!isOpen || !position) return null;

  return (
    <div
      className="fixed inset-0 z-[9999]"
      onClick={onClose}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div
        className="absolute backdrop-blur-xl bg-black/40 border border-white/[0.15] rounded-xl shadow-2xl min-w-[180px] py-3"
        style={{
          left: position.x,
          top: position.y,
          transform: 'translate(0, 0)',
          background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Reset View */}
        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onResetView();
            onClose();
          }}
        >
          <RotateCcw className="w-4 h-4" />
          Reset View
        </button>

        {/* Fit to Data */}
        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onFitToData();
            onClose();
          }}
        >
          <Maximize2 className="w-4 h-4" />
          Fit to Data
        </button>

        {/* Zoom Controls */}
        <div className="flex">
          <button
            className="flex-1 px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-2 text-sm font-medium"
            onClick={() => {
              onZoomIn();
              onClose();
            }}
          >
            <ZoomIn className="w-4 h-4" />
            Zoom In
          </button>

          <button
            className="flex-1 px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-2 text-sm font-medium"
            onClick={() => {
              onZoomOut();
              onClose();
            }}
          >
            <ZoomOut className="w-4 h-4" />
            Zoom Out
          </button>
        </div>

        <div className="h-px bg-white/[0.15] my-2 mx-2" />

        {/* Display Options */}
        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onToggleGrid();
            onClose();
          }}
        >
          <Grid3X3 className={`w-4 h-4 ${showGrid ? 'text-[#00e7b6]' : ''}`} />
          Grid
          {showGrid && <div className="ml-auto w-2 h-2 bg-[#00e7b6] rounded-full" />}
        </button>

        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onToggleCrosshair();
            onClose();
          }}
        >
          <Crosshair className={`w-4 h-4 ${showCrosshair ? 'text-[#00e7b6]' : ''}`} />
          Crosshair
          {showCrosshair && <div className="ml-auto w-2 h-2 bg-[#00e7b6] rounded-full" />}
        </button>

        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onToggleTooltip();
            onClose();
          }}
        >
          <Info className={`w-4 h-4 ${showTooltip ? 'text-[#00e7b6]' : ''}`} />
          Price Info
          {showTooltip && <div className="ml-auto w-2 h-2 bg-[#00e7b6] rounded-full" />}
        </button>

        <div className="h-px bg-white/[0.15] my-2 mx-2" />

        {/* Actions */}
        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onClearAllDrawings();
            onClose();
          }}
        >
          <Trash2 className="w-4 h-4" />
          Clear Drawings
        </button>

        <button
          className="w-full px-4 py-2.5 text-left text-white/90 hover:bg-white/[0.1] hover:text-white transition-all duration-200 flex items-center gap-3 text-sm font-medium"
          onClick={() => {
            onOpenSettings();
            onClose();
          }}
        >
          <Settings className="w-4 h-4" />
          Chart Settings
        </button>
      </div>
    </div>
  );
};

export default ChartContextMenu;
