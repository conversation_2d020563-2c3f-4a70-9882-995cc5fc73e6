import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, TrendingUp, Users, DollarSign, Trophy, 
  Calendar, Activity, Eye, Settings, ArrowLeft 
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Competition } from '@/hooks/useCompetitions';
import { formatDistanceToNow, format } from 'date-fns';

interface CompetitionCreatorDashboardProps {
  competition: Competition;
  onBack: () => void;
}

interface AnalyticsData {
  participantCount: number;
  totalRevenue: number;
  averageReturn: number;
  topPerformer: string;
  entryTrends: Array<{ date: string; count: number }>;
  recentActivity: Array<{ user: string; action: string; time: string }>;
}

const CompetitionCreatorDashboard: React.FC<CompetitionCreatorDashboardProps> = ({
  competition,
  onBack
}) => {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    participantCount: competition.participant_count || 0,
    totalRevenue: (competition.entry_fee || 0) * (competition.participant_count || 0),
    averageReturn: 2.4,
    topPerformer: 'Alex M.',
    entryTrends: [
      { date: 'Mon', count: 12 },
      { date: 'Tue', count: 19 },
      { date: 'Wed', count: 8 },
      { date: 'Thu', count: 15 },
      { date: 'Fri', count: 22 },
      { date: 'Sat', count: 7 },
      { date: 'Sun', count: 11 }
    ],
    recentActivity: [
      { user: 'Sarah K.', action: 'Joined competition', time: '2 minutes ago' },
      { user: 'Mike R.', action: 'Made a trade', time: '5 minutes ago' },
      { user: 'Emma L.', action: 'Joined competition', time: '12 minutes ago' },
      { user: 'David P.', action: 'Made a trade', time: '18 minutes ago' }
    ]
  });

  const getStatusColor = () => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (now < start) {
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    } else if (now > end) {
      return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    } else {
      return 'bg-green-500/20 text-green-400 border-green-500/30';
    }
  };

  const getStatusLabel = () => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (now < start) return 'Upcoming';
    if (now > end) return 'Completed';
    return 'Active';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
            className="border-white/[0.08] text-white/70 hover:text-white hover:bg-white/[0.04] active:scale-[0.98]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          
          <div>
            <h1 className="text-2xl font-medium text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
              {competition.name}
            </h1>
            <div className="flex items-center gap-3 mt-1">
              <Badge className={getStatusColor()}>
                {getStatusLabel()}
              </Badge>
              <span className="text-white/60 text-sm">
                Created {formatDistanceToNow(new Date(competition.created_at))} ago
              </span>
            </div>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          className="border-white/[0.08] text-white/70 hover:text-white hover:bg-white/[0.04] active:scale-[0.98]"
        >
          <Settings className="w-4 h-4 mr-2" />
          Settings
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Participants</p>
                <p className="text-2xl font-medium text-white">{analytics.participantCount}</p>
                {competition.max_participants && (
                  <p className="text-white/40 text-xs">
                    of {competition.max_participants} max
                  </p>
                )}
              </div>
              <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
                <Users className="w-5 h-5 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Revenue</p>
                <p className="text-2xl font-medium text-white">${analytics.totalRevenue}</p>
                <p className="text-white/40 text-xs">
                  ${competition.entry_fee || 0} per entry
                </p>
              </div>
              <div className="w-10 h-10 rounded-lg bg-green-500/20 flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Avg Return</p>
                <p className="text-2xl font-medium text-green-400">+{analytics.averageReturn}%</p>
                <p className="text-white/40 text-xs">across all participants</p>
              </div>
              <div className="w-10 h-10 rounded-lg bg-purple-500/20 flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Top Performer</p>
                <p className="text-lg font-medium text-white">{analytics.topPerformer}</p>
                <p className="text-green-400 text-xs">+12.4% return</p>
              </div>
              <div className="w-10 h-10 rounded-lg bg-yellow-500/20 flex items-center justify-center">
                <Trophy className="w-5 h-5 text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Entry Trends */}
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-green-400" />
              Entry Trends
            </CardTitle>
            <CardDescription>Participant registrations over the last 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.entryTrends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-white/60 text-sm">{trend.date}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 h-2 bg-white/[0.08] rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-green-500 rounded-full transition-all duration-500"
                        style={{ width: `${(trend.count / 25) * 100}%` }}
                      />
                    </div>
                    <span className="text-white text-sm font-medium w-8">{trend.count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-400" />
              Recent Activity
            </CardTitle>
            <CardDescription>Latest participant actions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <div>
                    <p className="text-white text-sm font-medium">{activity.user}</p>
                    <p className="text-white/60 text-xs">{activity.action}</p>
                  </div>
                  <span className="text-white/40 text-xs">{activity.time}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Competition Details */}
      <Card className="bg-white/[0.02] border-white/[0.08]">
        <CardHeader>
          <CardTitle className="text-white">Competition Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <p className="text-white/60 text-sm">Description</p>
                <p className="text-white text-sm mt-1">{competition.description || 'No description provided'}</p>
              </div>
              <div>
                <p className="text-white/60 text-sm">Starting Balance</p>
                <p className="text-white font-medium">${competition.starting_balance.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-white/60 text-sm">Prize Pool</p>
                <p className="text-white font-medium">${(competition.prize_pool || 0).toLocaleString()}</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <p className="text-white/60 text-sm">Duration</p>
                <p className="text-white text-sm">
                  {format(new Date(competition.competition_start), 'MMM dd, yyyy')} - {format(new Date(competition.competition_end), 'MMM dd, yyyy')}
                </p>
              </div>
              <div>
                <p className="text-white/60 text-sm">Visibility</p>
                <div className="flex items-center gap-2 mt-1">
                  {competition.competition_scope === 'public' ? (
                    <>
                      <Eye className="w-4 h-4 text-green-400" />
                      <span className="text-white text-sm">Public</span>
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 text-orange-400" />
                      <span className="text-white text-sm">Private</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CompetitionCreatorDashboard;
