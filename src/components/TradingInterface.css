/* Trading Interface Styles */

/* Backdrop */
.trading-interface-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 998;
  opacity: 1;
  animation: fadeIn 0.2s ease-out;
}

/* Main Sidebar */
.trading-interface-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.98) 0%, rgba(10, 10, 10, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.4);
  z-index: 999;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}

.trading-interface-sidebar.open {
  transform: translateX(0);
}

/* Header */
.trading-interface-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(180deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
}

.trading-symbol-badge {
  background: linear-gradient(135deg, rgba(0, 200, 83, 0.15) 0%, rgba(0, 200, 83, 0.05) 100%);
  border: 1px solid rgba(0, 200, 83, 0.2);
  color: #00C853;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.trading-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.trading-current-price {
  font-size: 16px;
  font-weight: 700;
  color: white;
}

.trading-close-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trading-close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Content */
.trading-interface-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Tabs */
.trading-tabs-list {
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 20px;
  width: 100%;
}

.trading-tab-buy {
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  transition: all 0.2s ease;
  flex: 1;
}

.trading-tab-buy[data-state="active"] {
  background: linear-gradient(135deg, rgba(0, 200, 83, 0.15) 0%, rgba(0, 200, 83, 0.05) 100%);
  color: #00C853;
  border: 1px solid rgba(0, 200, 83, 0.2);
  box-shadow: 0 2px 8px rgba(0, 200, 83, 0.1);
}

.trading-tab-sell {
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  transition: all 0.2s ease;
  flex: 1;
}

.trading-tab-sell[data-state="active"] {
  background: linear-gradient(135deg, rgba(213, 0, 0, 0.15) 0%, rgba(213, 0, 0, 0.05) 100%);
  color: #D50000;
  border: 1px solid rgba(213, 0, 0, 0.2);
  box-shadow: 0 2px 8px rgba(213, 0, 0, 0.1);
}

.trading-tab-content {
  margin-top: 0;
}

/* Form */
.trading-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trading-form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.trading-form-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  font-weight: 500;
}

.trading-input {
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  height: auto;
}

.trading-input:focus {
  border-color: rgba(0, 200, 83, 0.4);
  box-shadow: 0 0 0 2px rgba(0, 200, 83, 0.1);
  outline: none;
}

.trading-select {
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 8px;
  padding: 12px 16px;
  height: auto;
}

/* Cards */
.trading-balance-card {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
}

.trading-summary-card {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
}

.trading-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.trading-summary-row:last-child {
  margin-bottom: 0;
}

/* Submit Button */
.trading-submit-button {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.trading-submit-button.buy {
  background: linear-gradient(135deg, #00C853 0%, #00A843 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 200, 83, 0.3);
}

.trading-submit-button.buy:hover:not(:disabled) {
  background: linear-gradient(135deg, #00D85A 0%, #00C853 100%);
  box-shadow: 0 6px 16px rgba(0, 200, 83, 0.4);
  transform: translateY(-1px);
}

.trading-submit-button.sell {
  background: linear-gradient(135deg, #D50000 0%, #B71C1C 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(213, 0, 0, 0.3);
}

.trading-submit-button.sell:hover:not(:disabled) {
  background: linear-gradient(135deg, #E53935 0%, #D50000 100%);
  box-shadow: 0 6px 16px rgba(213, 0, 0, 0.4);
  transform: translateY(-1px);
}

.trading-submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Ensure proper z-index layering */
.trading-interface-backdrop {
  z-index: 998;
}

.trading-interface-sidebar {
  z-index: 999;
}

/* Smooth scrollbar for content */
.trading-interface-content::-webkit-scrollbar {
  width: 6px;
}

.trading-interface-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.trading-interface-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.trading-interface-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .trading-interface-sidebar {
    width: 100vw;
    right: 0;
  }

  .trading-interface-content {
    padding: 16px;
  }

  .trading-form {
    gap: 14px;
  }

  .trading-submit-button {
    height: 52px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .trading-interface-header {
    padding: 12px 16px;
  }

  .trading-interface-content {
    padding: 12px;
  }
}

/* Orders Panel Styles */
.orders-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(15, 15, 15, 0.98) 0%, rgba(10, 10, 10, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 -4px 24px rgba(0, 0, 0, 0.4);
  z-index: 997;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 33vh;
  display: flex;
  flex-direction: column;
}

.orders-panel.open {
  transform: translateY(0);
}

.orders-panel.minimized {
  max-height: 80px;
}

.orders-panel.minimized .orders-panel-header {
  padding: 20px 24px;
}

.orders-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(180deg, rgba(20, 20, 20, 0.95) 0%, rgba(15, 15, 15, 0.9) 100%);
  backdrop-filter: blur(12px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    0 2px 8px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
  min-height: 64px;
}

.orders-panel-header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.orders-panel-title {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  letter-spacing: 0.5px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.orders-panel-tabs {
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 3px;
  height: auto;
  backdrop-filter: blur(8px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.3);
}

.orders-panel-tab {
  background: transparent;
  color: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  font-weight: 600;
  padding: 8px 14px;
  height: auto;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: 0.3px;
}

.orders-panel-tab:hover {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.05);
}

.orders-panel-tab[data-state="active"] {
  background: linear-gradient(135deg,
    rgba(0, 231, 182, 0.2) 0%,
    rgba(0, 200, 160, 0.15) 50%,
    rgba(0, 180, 140, 0.2) 100%
  );
  color: #00e7b6;
  border: 1px solid rgba(0, 231, 182, 0.3);
  box-shadow:
    0 3px 12px rgba(0, 231, 182, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.4);
}

.orders-panel-control-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.orders-panel-control-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
}

.orders-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.orders-panel-tab-content {
  margin-top: 0;
}

.orders-panel-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

/* Clean Portfolio Lists */
.orders-list,
.positions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Clean Portfolio Cards */
.order-card,
.position-card {
  background: linear-gradient(145deg, rgba(20, 20, 20, 0.95) 0%, rgba(15, 15, 15, 0.9) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.order-card::before,
.position-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(0, 231, 182, 0.6) 0%,
    rgba(0, 200, 160, 0.4) 50%,
    rgba(0, 180, 140, 0.6) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.order-card:hover,
.position-card:hover {
  border-color: rgba(255, 255, 255, 0.15);
  background: linear-gradient(145deg, rgba(25, 25, 25, 0.95) 0%, rgba(20, 20, 20, 0.9) 100%);
  transform: translateY(-1px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 6px 20px rgba(0, 0, 0, 0.4),
    0 3px 6px rgba(0, 0, 0, 0.3);
}

.order-card:hover::before,
.position-card:hover::before {
  opacity: 1;
}

.order-type-badge {
  font-size: 10px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  backdrop-filter: blur(4px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.order-type-badge.buy {
  background: linear-gradient(135deg,
    rgba(0, 231, 182, 0.25) 0%,
    rgba(0, 200, 160, 0.15) 50%,
    rgba(0, 180, 140, 0.25) 100%
  );
  color: #00e7b6;
  border: 1px solid rgba(0, 231, 182, 0.3);
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.4);
}

.order-type-badge.sell {
  background: linear-gradient(135deg,
    rgba(255, 71, 87, 0.25) 0%,
    rgba(220, 38, 127, 0.15) 50%,
    rgba(255, 71, 87, 0.25) 100%
  );
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.3);
  text-shadow: 0 0 8px rgba(255, 71, 87, 0.4);
}

/* Enhanced order and position card details */
.order-details,
.position-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-symbol,
.position-symbol {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 0.5px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.order-meta,
.position-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.order-quantity,
.position-quantity {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
}

.order-price,
.position-price {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 400;
}

.order-value,
.position-value {
  font-size: 15px;
  font-weight: 700;
  color: #ffffff;
  text-align: right;
}

.position-pnl {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.position-pnl-amount {
  font-size: 15px;
  font-weight: 700;
  text-shadow: 0 0 8px currentColor;
}

.position-pnl-percent {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.9;
}

.position-pnl.positive .position-pnl-amount,
.position-pnl.positive .position-pnl-percent {
  color: #00e7b6;
}

.position-pnl.negative .position-pnl-amount,
.position-pnl.negative .position-pnl-percent {
  color: #ff4757;
}

.order-timestamp {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.4);
  font-weight: 400;
  text-align: right;
}

/* Status Icons */
.order-status-icon,
.position-status-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  flex-shrink: 0;
}

.order-status-icon.filled {
  background: linear-gradient(135deg, rgba(0, 231, 182, 0.2) 0%, rgba(0, 200, 160, 0.3) 100%);
  color: #00e7b6;
  border: 1px solid rgba(0, 231, 182, 0.3);
}

.order-status-icon.pending {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.3) 100%);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.order-status-icon.cancelled {
  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2) 0%, rgba(220, 38, 127, 0.3) 100%);
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

/* Premium Account Summary */
.trading-account-summary {
  background: linear-gradient(145deg, #141414 0%, #0F0F0F 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 16px 48px rgba(0, 0, 0, 0.5),
    0 8px 24px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.trading-account-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.trading-account-summary .grid {
  gap: 20px;
}

/* Premium Portfolio Typography */
.trading-account-summary .text-white\/60 {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8px;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trading-account-summary .text-lg {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.5px;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.1;
}



.trading-account-summary .text-green-500 {
  color: #00e7b6;
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
}

.trading-account-summary .text-red-500 {
  color: #ff4757;
  text-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.trading-account-summary .border-t {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 16px;
  padding-top: 16px;
}

.trading-account-summary .flex.justify-between span:first-child {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}

.trading-account-summary .flex.justify-between span:last-child {
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.3px;
}

/* Scrollbar for orders panel */
.orders-panel-content::-webkit-scrollbar {
  width: 6px;
}

.orders-panel-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.orders-panel-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.orders-panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile responsive for orders panel */
@media (max-width: 768px) {
  .orders-panel {
    max-height: 35vh;
  }

  .orders-panel-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .orders-panel-header-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    flex: 1;
  }

  .orders-panel-header > div:last-child {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    align-self: flex-start;
  }

  .orders-panel-content {
    padding: 12px 16px;
  }

  .orders-panel-title {
    font-size: 14px;
  }

  .orders-panel-tabs {
    width: 100%;
  }

  .orders-panel-tab {
    font-size: 11px;
    padding: 4px 8px;
  }

  .orders-panel.minimized {
    max-height: 70px;
  }

  .orders-panel.minimized .orders-panel-header {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .orders-panel {
    max-height: 30vh;
  }

  .orders-panel-header {
    padding: 10px 12px;
  }

  .orders-panel-content {
    padding: 10px 12px;
  }

  .orders-panel.minimized {
    max-height: 65px;
  }

  .orders-panel.minimized .orders-panel-header {
    padding: 14px 16px;
  }
}

/* Trading mode selector */
.trading-mode-selector {
  margin-bottom: 16px;
}

.trading-mode-selector label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
  color: rgba(255, 255, 255, 0.8);
}

/* Account summary card */
.trading-account-summary {
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
}

.trading-account-summary .grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.trading-account-summary .text-gray-500 {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
  margin-bottom: 2px;
}

.trading-account-summary .font-semibold {
  font-weight: 600;
  font-size: 12px;
  color: white;
}

.trading-account-summary .text-green-600 {
  color: #00C853;
}

.trading-account-summary .text-red-600 {
  color: #D50000;
}

/* Position summary card */
.trading-position-summary {
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
}

.trading-position-summary .grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.trading-position-summary .text-gray-500 {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
  margin-bottom: 2px;
}

.trading-position-summary .font-semibold {
  font-weight: 600;
  font-size: 12px;
  color: white;
}

.trading-position-summary .text-green-600 {
  color: #00C853;
}

.trading-position-summary .text-red-600 {
  color: #D50000;
}

/* Account summary in orders panel */
.orders-panel .trading-account-summary {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.orders-panel .trading-position-summary {
  background: linear-gradient(135deg, rgba(20, 20, 20, 0.9) 0%, rgba(15, 15, 15, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.orders-panel .trading-account-summary .grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.orders-panel .trading-position-summary .grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

/* Mobile responsive for new components */
@media (max-width: 768px) {
  .trading-account-summary .grid,
  .trading-position-summary .grid,
  .orders-panel .trading-account-summary .grid,
  .orders-panel .trading-position-summary .grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }
}
