import React, { useEffect, useRef } from 'react';

interface MiniLineChartProps {
  data: { time: number; price: number }[];
  width?: number;
  height?: number;
  color?: string;
  strokeWidth?: number;
  className?: string;
}

const MiniLineChart: React.FC<MiniLineChartProps> = ({
  data,
  width = 120,
  height = 40,
  color = '#00e7b6',
  strokeWidth = 1.5,
  className = ''
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const svg = svgRef.current;
    if (!svg) return;

    // Clear previous content
    svg.innerHTML = '';

    // Calculate price range
    const prices = data.map(d => d.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    // Handle case where all prices are the same
    if (priceRange === 0) {
      // Draw a flat line in the middle
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', '0');
      line.setAttribute('y1', (height / 2).toString());
      line.setAttribute('x2', width.toString());
      line.setAttribute('y2', (height / 2).toString());
      line.setAttribute('stroke', color);
      line.setAttribute('stroke-width', strokeWidth.toString());
      line.setAttribute('fill', 'none');
      svg.appendChild(line);
      return;
    }

    // Create path for the line with better edge-to-edge fitting
    const pathData = data.map((point, index) => {
      // Use full width from edge to edge, no padding
      const x = data.length === 1 ? width / 2 : (index / (data.length - 1)) * width;
      const y = height - ((point.price - minPrice) / priceRange) * height;
      return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
    }).join(' ');

    // Create the line path
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', pathData);
    path.setAttribute('stroke', color);
    path.setAttribute('stroke-width', strokeWidth.toString());
    path.setAttribute('fill', 'none');
    path.setAttribute('stroke-linecap', 'round');
    path.setAttribute('stroke-linejoin', 'round');

    // Add subtle glow effect with dynamic color
    const glowColor = color === '#00e7b6' ? 'rgba(0, 231, 182, 0.4)' : 'rgba(239, 68, 68, 0.4)';
    path.setAttribute('filter', `drop-shadow(0 0 3px ${glowColor})`);

    svg.appendChild(path);

    // Add gradient fill area under the line with improved styling
    const gradientId = `gradient-${Math.random().toString(36).substr(2, 9)}`;

    // Create gradient definition
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    gradient.setAttribute('id', gradientId);
    gradient.setAttribute('x1', '0%');
    gradient.setAttribute('y1', '0%');
    gradient.setAttribute('x2', '0%');
    gradient.setAttribute('y2', '100%');

    const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop1.setAttribute('offset', '0%');
    stop1.setAttribute('stop-color', color);
    stop1.setAttribute('stop-opacity', '0.15');

    const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop2.setAttribute('offset', '70%');
    stop2.setAttribute('stop-color', color);
    stop2.setAttribute('stop-opacity', '0.05');

    const stop3 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop3.setAttribute('offset', '100%');
    stop3.setAttribute('stop-color', color);
    stop3.setAttribute('stop-opacity', '0');

    gradient.appendChild(stop1);
    gradient.appendChild(stop2);
    gradient.appendChild(stop3);
    defs.appendChild(gradient);
    svg.appendChild(defs);

    // Create filled area path
    const fillPathData = pathData + ` L ${width} ${height} L 0 ${height} Z`;
    const fillPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    fillPath.setAttribute('d', fillPathData);
    fillPath.setAttribute('fill', `url(#${gradientId})`);

    // Insert fill path before the line
    svg.insertBefore(fillPath, path);

  }, [data, width, height, color, strokeWidth]);

  if (!data || data.length === 0) {
    return (
      <div 
        className={`flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <div className="text-xs text-white/40">No data</div>
      </div>
    );
  }

  return (
    <svg
      ref={svgRef}
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      preserveAspectRatio="none"
      className={`${className}`}
      style={{
        overflow: 'visible',
        width: '100%',
        height: '100%'
      }}
    />
  );
};

export default MiniLineChart;
