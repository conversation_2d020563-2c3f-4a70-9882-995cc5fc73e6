import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { ExternalLink, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FallbackChartProps {
  symbol: string;
  interval?: string;
  theme?: 'light' | 'dark';
  width?: string | number;
  height?: string | number;
  startDate?: string;
  endDate?: string;
}

const FallbackChart: React.FC<FallbackChartProps> = ({
  symbol,
  interval = 'D',
  theme = 'dark',
  width = '100%',
  height = 500,
  startDate,
  endDate
}) => {
  // Format symbol for TradingView
  const formattedSymbol = symbol.includes(':') ? symbol : `NASDAQ:${symbol}`;
  
  // Create TradingView URL for external link
  const tradingViewUrl = `https://www.tradingview.com/chart/?symbol=${encodeURIComponent(formattedSymbol)}&interval=${interval}`;

  // Calculate trade period info
  let tradePeriodInfo = '';
  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    tradePeriodInfo = `Trade period: ${diffDays} day${diffDays > 1 ? 's' : ''}`;
  }

  return (
    <div 
      className="tradingview-fallback-container"
      style={{ 
        width: typeof width === 'string' ? width : `${width}px`,
        height: typeof height === 'string' ? height : `${height}px`,
        minHeight: '400px'
      }}
    >
      <Card className="h-full bg-[#0F0F11] border-[#1A1A1C]">
        <CardHeader className="text-center">
          <CardTitle className="text-white text-lg flex items-center justify-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-400" />
            Chart Analysis for {symbol}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center h-full space-y-6">
          {/* Chart placeholder */}
          <div className="w-full h-48 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-lg border border-white/10 flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 text-blue-400 mx-auto mb-2" />
              <p className="text-white/60 text-sm">Chart visualization</p>
              <p className="text-white/40 text-xs">View on TradingView for detailed analysis</p>
            </div>
          </div>

          {/* Trade info */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <span className="text-white/60">Symbol:</span>
                <span className="text-white font-mono">{symbol}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-white/60">Interval:</span>
                <span className="text-white font-mono">{interval}</span>
              </div>
            </div>
            
            {tradePeriodInfo && (
              <p className="text-white/50 text-xs">{tradePeriodInfo}</p>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={() => window.open(tradingViewUrl, '_blank')}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View on TradingView
            </Button>
            
            <Button
              onClick={() => window.open(`https://finance.yahoo.com/quote/${symbol}`, '_blank')}
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Yahoo Finance
            </Button>
          </div>

          {/* Trade period visualization */}
          {startDate && endDate && (
            <div className="w-full max-w-md">
              <div className="text-center mb-2">
                <p className="text-white/60 text-xs">Trade Timeline</p>
              </div>
              <div className="relative">
                <div className="h-2 bg-white/10 rounded-full">
                  <div className="h-2 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full w-full"></div>
                </div>
                <div className="flex justify-between mt-1 text-xs text-white/50">
                  <span>Entry: {new Date(startDate).toLocaleDateString()}</span>
                  <span>Exit: {new Date(endDate).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          )}

          {/* Note */}
          <div className="text-center">
            <p className="text-white/40 text-xs max-w-md">
              For detailed technical analysis with indicators, annotations, and real-time data, 
              click "View on TradingView" to open the full chart in a new tab.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FallbackChart;
