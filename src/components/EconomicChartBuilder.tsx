import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Sparkles, TrendingUp } from 'lucide-react';
import SocialMediaChartGenerator, { ChartConfig } from './SocialMediaChartGenerator';
import { fetchCompanyFinancials, CompanyFinancials } from '@/services/economicDataService';
import { generateChartDataWithAI, sampleQueries } from '@/services/aiChartDataService';
import { useAuth } from '@/contexts/AuthContext';

interface EconomicChartBuilderProps {
  onExport?: (imageData: string) => void;
}

const EconomicChartBuilder: React.FC<EconomicChartBuilderProps> = ({ onExport }) => {
  const { user } = useAuth();

  // Traditional financial data states
  const [symbol, setSymbol] = useState('AAPL');
  const [loading, setLoading] = useState(false);
  const [financials, setFinancials] = useState<CompanyFinancials | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<keyof CompanyFinancials>('revenue');
  const [chartConfig, setChartConfig] = useState<ChartConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  // AI-powered chart generation states
  const [aiQuery, setAiQuery] = useState('');
  const [aiLoading, setAiLoading] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('ai');

  const metricOptions = [
    { key: 'revenue', label: 'Revenue', color: '#3B82F6' },
    { key: 'netIncome', label: 'Net Income', color: '#10B981' },
    { key: 'grossProfit', label: 'Gross Profit', color: '#F59E0B' },
    { key: 'operatingIncome', label: 'Operating Income', color: '#8B5CF6' },
    { key: 'totalAssets', label: 'Total Assets', color: '#06B6D4' },
    { key: 'totalDebt', label: 'Total Debt', color: '#EF4444' },
    { key: 'freeCashFlow', label: 'Free Cash Flow', color: '#84CC16' }
  ];

  const fetchData = async () => {
    if (!symbol.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const data = await fetchCompanyFinancials(symbol.toUpperCase());
      setFinancials(data);
      generateChart(data, selectedMetric);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
      setFinancials(null);
      setChartConfig(null);
    } finally {
      setLoading(false);
    }
  };

  const generateChart = (data: CompanyFinancials, metric: keyof CompanyFinancials) => {
    if (!data || metric === 'symbol') return;

    const metricData = data[metric] as any[];
    if (!metricData || metricData.length === 0) {
      setError(`No ${metric} data available for ${data.symbol}`);
      return;
    }

    const metricInfo = metricOptions.find(m => m.key === metric);
    const config: ChartConfig = {
      title: `${data.symbol} - ${metricInfo?.label || metric}`,
      subtitle: `Quarterly data (in millions)`,
      type: 'bar',
      data: metricData,
      colors: [metricInfo?.color || '#3B82F6'],
      showValues: true,
      showGrid: true,
      backgroundColor: '#FFFFFF',
      textColor: '#000000',
      width: 800,
      height: 600,
      brandText: 'Made with Osis.co',
      valueFormatter: (value: number) => {
        if (Math.abs(value) >= 1000) {
          return `${(value / 1000).toFixed(1)}B`;
        }
        return `${value.toFixed(0)}M`;
      }
    };

    // Calculate growth metrics
    if (metricData.length >= 2) {
      const firstValue = metricData[0].value;
      const lastValue = metricData[metricData.length - 1].value;
      const totalChange = ((lastValue - firstValue) / Math.abs(firstValue)) * 100;
      
      // Calculate CAGR (assuming quarterly data)
      const periods = metricData.length - 1;
      const years = periods / 4; // Convert quarters to years
      const cagr = years > 0 ? (Math.pow(lastValue / firstValue, 1 / years) - 1) * 100 : 0;
      
      config.totalChange = `${totalChange.toFixed(1)}%`;
      config.cagr = `${cagr.toFixed(1)}%`;
    }

    setChartConfig(config);
  };

  useEffect(() => {
    if (financials) {
      generateChart(financials, selectedMetric);
    }
  }, [selectedMetric, financials]);

  const handleMetricChange = (metric: string) => {
    setSelectedMetric(metric as keyof CompanyFinancials);
  };

  const generateAIChart = async () => {
    if (!aiQuery.trim()) return;

    setAiLoading(true);
    setAiError(null);

    try {
      const response = await generateChartDataWithAI({
        query: aiQuery,
        userId: user?.id
      });

      if (response.success && response.chartConfig) {
        setChartConfig(response.chartConfig);
        // Clear traditional data states when using AI
        setFinancials(null);
      } else {
        setAiError(response.error || 'Failed to generate chart');
      }
    } catch (err) {
      setAiError(err instanceof Error ? err.message : 'Failed to generate chart');
    } finally {
      setAiLoading(false);
    }
  };

  const handleSampleQuery = (query: string) => {
    setAiQuery(query);
  };

  const generateTestChart = () => {
    const testConfig: ChartConfig = {
      title: 'Test Chart - Sample Data',
      subtitle: 'This is a test chart with hardcoded data',
      type: 'bar',
      data: [
        { name: 'Jan 2024', value: 1200 },
        { name: 'Feb 2024', value: 1450 },
        { name: 'Mar 2024', value: 1680 },
        { name: 'Apr 2024', value: 1920 },
        { name: 'May 2024', value: 2150 }
      ],
      colors: ['#3B82F6'],
      showValues: true,
      showGrid: true,
      backgroundColor: '#FFFFFF',
      textColor: '#000000',
      width: 800,
      height: 600,
      brandText: 'Made with Osis.co',
      totalChange: '79.2%',
      cagr: '15.8%'
    };

    setChartConfig(testConfig);
    setFinancials(null);
    setError(null);
    setAiError(null);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6 p-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="ai" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            AI Chart Generator
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Financial Data
          </TabsTrigger>
        </TabsList>

        <TabsContent value="ai" className="space-y-4">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Ask for any chart data in natural language
              </label>
              <Textarea
                value={aiQuery}
                onChange={(e) => setAiQuery(e.target.value)}
                placeholder="e.g., 'Show me Apple's revenue growth over the last 5 quarters' or 'Compare Tesla vs Ford stock performance'"
                rows={3}
                className="resize-none"
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={generateAIChart}
                disabled={aiLoading || !aiQuery.trim()}
                className="flex-1"
              >
                {aiLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating Chart...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Chart with AI
                  </>
                )}
              </Button>

              <Button
                onClick={generateTestChart}
                variant="outline"
                className="px-4"
              >
                Test Chart
              </Button>
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-600">Try these examples:</p>
              <div className="flex flex-wrap gap-2">
                {sampleQueries.slice(0, 6).map((query, index) => (
                  <button
                    key={index}
                    onClick={() => handleSampleQuery(query)}
                    className="text-xs px-3 py-1 bg-blue-50 text-blue-700 rounded-full hover:bg-blue-100 transition-colors"
                  >
                    {query}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {aiError && (
            <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
              <p className="text-red-600">{aiError}</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Input
                value={symbol}
                onChange={(e) => setSymbol(e.target.value.toUpperCase())}
                placeholder="Enter stock symbol (e.g., AAPL)"
                className="uppercase"
              />
            </div>

            {financials && (
              <div className="flex-1">
                <Select value={selectedMetric} onValueChange={handleMetricChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select metric" />
                  </SelectTrigger>
                  <SelectContent>
                    {metricOptions.map(option => {
                      const data = financials[option.key as keyof CompanyFinancials] as any[];
                      const hasData = Array.isArray(data) && data.length > 0;
                      return (
                        <SelectItem
                          key={option.key}
                          value={option.key}
                          disabled={!hasData}
                        >
                          {option.label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            )}

            <Button
              onClick={fetchData}
              disabled={loading || !symbol.trim()}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                'Generate'
              )}
            </Button>
          </div>

          {error && (
            <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {chartConfig && (
        <div className="w-full">
          <SocialMediaChartGenerator
            config={chartConfig}
            onExport={onExport}
          />
        </div>
      )}
    </div>
  );
};

export default EconomicChartBuilder;
