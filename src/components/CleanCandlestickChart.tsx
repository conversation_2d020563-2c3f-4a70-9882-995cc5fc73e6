import React, { useRef, useEffect, useState, useCallback } from 'react';
import { format } from 'date-fns';
import { FocusedOHLCVData } from '@/services/tradeAnalysisService';

interface TradeMarker {
  timestamp: number;
  price: number;
  type: 'entry' | 'exit';
  label: string;
  color: string;
  tradeIndex?: number; // Add trade index for navigation
}

interface ChartIndicator {
  type: 'moving_average' | 'momentum_indicator' | 'trend_indicator' | 'volume_indicator' | 'volatility_indicator';
  indicator: string;
  data: any;
  parameters: Record<string, any>;
  color?: string;
  lineWidth?: number;
}

interface SupportResistanceLevel {
  price: number;
  type: 'support' | 'resistance';
  strength: number;
  touches: number;
  color?: string;
  label?: string;
}

interface TrendLine {
  startTimestamp: number;
  startPrice: number;
  endTimestamp: number;
  endPrice: number;
  type: 'trend' | 'channel';
  color?: string;
  lineWidth?: number;
}

interface BreakoutZone {
  timestamp: number;
  price: number;
  type: 'bullish' | 'bearish';
  level: number;
  color?: string;
}

interface CleanCandlestickChartProps {
  symbol: string;
  data: FocusedOHLCVData[];
  tradeMarkers: TradeMarker[];
  height?: number;
  className?: string;
  currentTradeIndex?: number;
  onNavigateToTrade?: (tradeIndex: number) => void;
  allTrades?: any[]; // All trades for timeline navigation
  // New props for agent indicators and overlays
  indicators?: ChartIndicator[];
  supportResistanceLevels?: SupportResistanceLevel[];
  trendLines?: TrendLine[];
  breakoutZones?: BreakoutZone[];
}

const CleanCandlestickChart: React.FC<CleanCandlestickChartProps> = ({
  symbol,
  data,
  tradeMarkers,
  height = 400,
  className = '',
  currentTradeIndex,
  onNavigateToTrade,
  allTrades = [],
  indicators = [],
  supportResistanceLevels = [],
  trendLines = [],
  breakoutZones = []
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height });

  // TradingView-style state
  const [timeOffset, setTimeOffset] = useState(0); // Time offset in milliseconds (how much we've panned)
  const [timeZoom, setTimeZoom] = useState(1); // Time zoom level (1 = show all data)
  const [priceScale, setPriceScale] = useState(1); // Price zoom level
  const [isDragging, setIsDragging] = useState(false);
  const [isPriceScaling, setIsPriceScaling] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [crosshair, setCrosshair] = useState<{ x: number; y: number; price: number; time: number } | null>(null);

  // Timeline navigation logic
  const canNavigatePrevious = currentTradeIndex !== undefined && currentTradeIndex > 0;
  const canNavigateNext = currentTradeIndex !== undefined && currentTradeIndex < allTrades.length - 1;

  const handlePreviousTrade = () => {
    if (canNavigatePrevious && onNavigateToTrade && currentTradeIndex !== undefined) {
      onNavigateToTrade(currentTradeIndex - 1);
    }
  };

  const handleNextTrade = () => {
    if (canNavigateNext && onNavigateToTrade && currentTradeIndex !== undefined) {
      onNavigateToTrade(currentTradeIndex + 1);
    }
  };

  // Reset chart view
  const resetView = () => {
    setTimeOffset(0);
    setTimeZoom(1);
    setPriceScale(1);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousTrade();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextTrade();
      } else if (event.key === 'r' || event.key === 'R') {
        // Reset view with 'R' key
        event.preventDefault();
        resetView();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTradeIndex, canNavigatePrevious, canNavigateNext]);

  // Handle canvas resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height: height - 70 }); // Account for larger header
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [height]);

  // Calculate chart bounds with proper TradingView-style behavior
  const chartBounds = React.useMemo(() => {
    if (data.length === 0) {
      return {
        xMin: Date.now() - 24 * 60 * 60 * 1000,
        xMax: Date.now(),
        yMin: 100,
        yMax: 200
      };
    }

    const timestamps = data.map(d => d.timestamp);
    const prices = data.flatMap(d => [d.high, d.low]);

    // Include trade marker prices in bounds calculation
    const tradeMarkerPrices = tradeMarkers.map(m => m.price);
    const allPrices = [...prices, ...tradeMarkerPrices];

    // Base data bounds
    const baseXMin = Math.min(...timestamps);
    const baseXMax = Math.max(...timestamps);
    const baseYMin = Math.min(...allPrices);
    const baseYMax = Math.max(...allPrices);

    // TradingView-style X-axis calculation
    const baseTimeRange = baseXMax - baseXMin;

    // Apply time zoom (smaller zoom = more data visible)
    const visibleTimeRange = baseTimeRange / timeZoom;

    // Calculate the center point for the visible window
    const timeCenter = baseXMin + baseTimeRange / 2;

    // Apply time offset (panning) - this shifts the center
    const adjustedTimeCenter = timeCenter + timeOffset;

    // Calculate final visible time bounds
    const xMin = adjustedTimeCenter - visibleTimeRange / 2;
    const xMax = adjustedTimeCenter + visibleTimeRange / 2;

    // Perfect fit Y-axis: No padding - highest candle touches top, lowest touches bottom
    const basePriceRange = baseYMax - baseYMin;

    // Ensure minimum price range to prevent division by zero
    const minPriceRange = Math.max(basePriceRange * 0.1, 0.01);
    const effectivePriceRange = Math.max(basePriceRange, minPriceRange);

    const scaledPriceRange = effectivePriceRange / priceScale;
    const priceCenter = (baseYMin + baseYMax) / 2;

    // NO padding - perfect fit with candles touching chart edges
    const yMin = priceCenter - scaledPriceRange / 2;
    const yMax = priceCenter + scaledPriceRange / 2;

    return {
      xMin,
      xMax,
      yMin,
      yMax
    };
  }, [data, tradeMarkers, timeOffset, timeZoom, priceScale]);

  // Handle zoom with mouse wheel (TradingView style)
  const handleWheel = useCallback((event: WheelEvent) => {
    event.preventDefault();

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;

    // Check if mouse is over price scale (right side)
    if (mouseX > margin.left + chartWidth && mouseX <= canvasSize.width - margin.right) {
      // Price scaling (Y-axis zoom)
      const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;
      setPriceScale(prev => Math.max(0.1, Math.min(10, prev * scaleFactor)));
    } else if (mouseX >= margin.left && mouseX <= margin.left + chartWidth) {
      // Time scaling (X-axis zoom) - TradingView style
      const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;

      // Calculate the time at mouse position before zoom
      const relativeX = (mouseX - margin.left) / chartWidth;
      const timeRange = chartBounds.xMax - chartBounds.xMin;
      const mouseTime = chartBounds.xMin + relativeX * timeRange;

      // Apply time zoom
      setTimeZoom(prev => Math.max(0.1, Math.min(10, prev * zoomFactor)));

      // Adjust time offset to keep mouse position stable during zoom
      const currentCenter = chartBounds.xMin + timeRange / 2;
      const offsetToMouse = mouseTime - currentCenter;
      const zoomAdjustment = offsetToMouse * (1 - 1/zoomFactor);
      setTimeOffset(prev => prev + zoomAdjustment);
    }
  }, [chartBounds, canvasSize]);

  // TradingView-style mouse handlers
  const handleMouseDown = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;

    // Check if mouse is over price scale (right side) for price scaling
    if (mouseX > margin.left + chartWidth && mouseX <= canvasSize.width - margin.right) {
      setIsPriceScaling(true);
    } else if (mouseX >= margin.left && mouseX <= margin.left + chartWidth) {
      setIsDragging(true);
    }

    setLastMousePos({ x: event.clientX, y: event.clientY });
  }, [canvasSize]);

  const handleNativeMouseMove = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    if (isDragging) {
      // TradingView-style horizontal panning - convert pixel movement to time movement
      const deltaX = event.clientX - lastMousePos.x;

      // Calculate how much time this pixel movement represents
      const timeRange = chartBounds.xMax - chartBounds.xMin;
      const margin = { top: 20, right: 80, bottom: 50, left: 20 };
      const chartWidth = canvasSize.width - margin.left - margin.right;
      const pixelsPerMs = chartWidth / timeRange;
      const deltaTime = deltaX / pixelsPerMs;

      // Apply time offset - positive deltaX should move chart right (show earlier data)
      setTimeOffset(prev => prev - deltaTime);
      setLastMousePos({ x: event.clientX, y: event.clientY });
    } else if (isPriceScaling) {
      // TradingView-style price scaling
      const deltaY = event.clientY - lastMousePos.y;
      const scaleFactor = 1 + (deltaY * 0.01); // Sensitivity adjustment
      setPriceScale(prev => Math.max(0.1, Math.min(10, prev * scaleFactor)));
      setLastMousePos({ x: event.clientX, y: event.clientY });
    } else {
      // Update crosshair position for TradingView-style info display
      const margin = { top: 20, right: 80, bottom: 50, left: 20 };
      const chartWidth = canvasSize.width - margin.left - margin.right;
      const chartHeight = canvasSize.height - margin.top - margin.bottom;

      if (x >= margin.left && x <= margin.left + chartWidth &&
          y >= margin.top && y <= margin.top + chartHeight) {

        // Calculate price and time at crosshair
        const timeRange = chartBounds.xMax - chartBounds.xMin;
        const priceRange = chartBounds.yMax - chartBounds.yMin;
        const relativeX = (x - margin.left) / chartWidth;
        const relativeY = (y - margin.top) / chartHeight;

        const time = chartBounds.xMin + relativeX * timeRange;
        const price = chartBounds.yMax - relativeY * priceRange;

        setCrosshair({ x, y, price, time });
      } else {
        setCrosshair(null);
      }
    }
  }, [isDragging, isPriceScaling, lastMousePos, canvasSize, chartBounds]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsPriceScaling(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDragging(false);
    setIsPriceScaling(false);
    setCrosshair(null);
  }, []);

  // Add event listeners
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('wheel', handleWheel);
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleNativeMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleNativeMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [handleWheel, handleMouseDown, handleNativeMouseMove, handleMouseUp, handleMouseLeave]);



  // Draw the chart
  const drawChart = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || data.length === 0) return;

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasSize.width * dpr;
    canvas.height = canvasSize.height * dpr;
    canvas.style.width = `${canvasSize.width}px`;
    canvas.style.height = `${canvasSize.height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas with dark background
    ctx.fillStyle = '#101010';
    ctx.fillRect(0, 0, canvasSize.width, canvasSize.height);

    // Chart margins
    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;

    // Grid removed for cleaner appearance

    // Draw candlesticks
    drawCandlesticks(ctx, margin, chartWidth, chartHeight);

    // Draw indicators and overlays
    drawIndicators(ctx, margin, chartWidth, chartHeight);
    drawSupportResistanceLevels(ctx, margin, chartWidth, chartHeight);
    drawTrendLines(ctx, margin, chartWidth, chartHeight);
    drawBreakoutZones(ctx, margin, chartWidth, chartHeight);

    // Draw trade markers (on top of indicators)
    drawTradeMarkers(ctx, margin, chartWidth, chartHeight);

    // Draw axes with TradingView-style info
    drawYAxisLabels(ctx, margin, chartWidth, chartHeight);
    drawXAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Draw crosshair and price/time info (TradingView style)
    if (crosshair) {
      drawCrosshair(ctx, crosshair, margin, chartWidth, chartHeight);
    }
  }, [data, canvasSize, tradeMarkers, chartBounds, crosshair, indicators, supportResistanceLevels, trendLines, breakoutZones]);

  // Grid function removed for cleaner chart appearance

  // Draw candlesticks
  const drawCandlesticks = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (data.length === 0) return;

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const priceRange = chartBounds.yMax - chartBounds.yMin;

    // TradingView-style spacing: Fixed candle spacing regardless of zoom
    // Calculate time per pixel to determine proper candle spacing
    const msPerPixel = timeRange / chartWidth;

    // Estimate average time between candles from the data
    let avgCandleInterval = 60 * 60 * 1000; // Default to 1 hour
    if (data.length > 1) {
      const intervals = [];
      for (let i = 1; i < Math.min(data.length, 10); i++) {
        intervals.push(data[i].timestamp - data[i-1].timestamp);
      }
      avgCandleInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    }

    // Calculate pixels per candle based on time interval
    const pixelsPerCandle = avgCandleInterval / msPerPixel;

    // TradingView-style candle width: 60-80% of available space per candle
    let candleWidth = Math.max(1, pixelsPerCandle * 0.7);

    // Set reasonable bounds for different zoom levels
    candleWidth = Math.max(1, Math.min(30, candleWidth));

    // Filter candles that would be visible (with some buffer for partial visibility)
    const buffer = candleWidth;
    const visibleCandles = data.filter(candle => {
      const x = margin.left + ((candle.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      return x >= margin.left - buffer && x <= margin.left + chartWidth + buffer;
    });

    console.log(`[Chart] TradingView-style rendering:`, {
      visibleCandles: visibleCandles.length,
      timeRange: timeRange,
      candleWidth: candleWidth.toFixed(2),
      pixelsPerCandle: pixelsPerCandle.toFixed(2),
      avgCandleInterval: (avgCandleInterval / (60 * 60 * 1000)).toFixed(2) + 'h',
      msPerPixel: msPerPixel.toFixed(0),
      priceScale: priceScale.toFixed(2),
      timeOffset: timeOffset.toFixed(0),
      timeZoom: timeZoom.toFixed(2)
    });

    // Draw each visible candle with proper candlestick appearance
    visibleCandles.forEach((candle) => {
      // Calculate X position based on timestamp (same as trade markers)
      const x = Math.round(margin.left + ((candle.timestamp - chartBounds.xMin) / timeRange) * chartWidth);

      // Calculate Y positions based on prices with pixel precision
      const highY = Math.round(margin.top + chartHeight - ((candle.high - chartBounds.yMin) / priceRange) * chartHeight);
      const lowY = Math.round(margin.top + chartHeight - ((candle.low - chartBounds.yMin) / priceRange) * chartHeight);
      const openY = Math.round(margin.top + chartHeight - ((candle.open - chartBounds.yMin) / priceRange) * chartHeight);
      const closeY = Math.round(margin.top + chartHeight - ((candle.close - chartBounds.yMin) / priceRange) * chartHeight);

      // Skip candles that are outside the visible area
      if (x < margin.left - candleWidth || x > margin.left + chartWidth + candleWidth) {
        return;
      }

      const isGreen = candle.close >= candle.open;

      // Clean futuristic colors - only green and white, no grey
      const bullishColor = '#34d399'; // Emerald-400 - bright green highlighter
      const bearishColor = '#ffffff'; // Clean white

      // Calculate body dimensions with proper TradingView-style sizing
      const bodyTop = Math.min(openY, closeY);
      const bodyBottom = Math.max(openY, closeY);

      // Dynamic minimum height based on zoom level and candle width
      // When zoomed in (larger candles), use larger minimum height for better visibility
      const baseMinHeight = Math.max(2, Math.round(candleWidth * 0.15)); // Scale with candle width
      const zoomAdjustedMinHeight = Math.max(baseMinHeight, Math.round(priceScale * 3)); // Scale with price zoom
      const bodyHeight = Math.max(bodyBottom - bodyTop, zoomAdjustedMinHeight);

      // TradingView-style body width: slightly smaller than total candle width for spacing
      const actualBodyWidth = Math.max(1, Math.round(candleWidth * 0.8));
      const bodyLeft = Math.round(x - actualBodyWidth / 2);
      const bodyWidth = actualBodyWidth;

      // Choose colors based on direction only (no grey doji)
      const candleColor = isGreen ? bullishColor : bearishColor;
      const wickColor = candleColor;

      // Draw wick (high-low line) first - behind the body
      if (highY !== lowY) { // Only draw wick if there's a range
        ctx.strokeStyle = wickColor;
        // TradingView-style wick: thinner relative to body, with reasonable bounds
        const wickWidth = Math.max(1, Math.min(2, Math.round(candleWidth * 0.1)));
        ctx.lineWidth = wickWidth;
        ctx.lineCap = 'butt';

        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();
      }

      // Draw candle body - always use green or white
      // Check if this is a very small price movement (doji-like)
      const actualPriceRange = Math.abs(candle.close - candle.open);
      const isVerySmallBody = bodyHeight <= baseMinHeight || actualPriceRange < (priceRange * 0.001);

      if (isVerySmallBody) {
        // Very small body - draw as thick line but ensure it's visible when zoomed in
        ctx.strokeStyle = candleColor;
        const lineThickness = Math.max(2, Math.min(bodyWidth, Math.round(candleWidth * 0.4)));
        ctx.lineWidth = lineThickness;
        ctx.lineCap = 'butt';
        ctx.beginPath();
        ctx.moveTo(bodyLeft, Math.round((bodyTop + bodyBottom) / 2));
        ctx.lineTo(bodyLeft + bodyWidth, Math.round((bodyTop + bodyBottom) / 2));
        ctx.stroke();
      } else {
        // Regular candle body
        ctx.fillStyle = candleColor;
        ctx.fillRect(bodyLeft, bodyTop, bodyWidth, bodyHeight);

        // Add subtle border for definition (only for white candles)
        if (!isGreen) {
          ctx.strokeStyle = '#d1d5db';
          ctx.lineWidth = 1;
          ctx.strokeRect(bodyLeft, bodyTop, bodyWidth, bodyHeight);
        }
      }
    });
  };

  // Draw clean entry candle highlights
  const drawTradeMarkers = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (tradeMarkers.length === 0) {
      return;
    }

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const priceRange = chartBounds.yMax - chartBounds.yMin;

    tradeMarkers.forEach((marker) => {
      // Skip markers that are outside the visible viewport
      if (marker.timestamp < chartBounds.xMin || marker.timestamp > chartBounds.xMax) {
        return;
      }

      const isCurrentTrade = marker.tradeIndex === currentTradeIndex;

      // Find the closest candle to this entry timestamp
      const entryCandle = data.reduce((closest, candle) => {
        const currentDiff = Math.abs(candle.timestamp - marker.timestamp);
        const closestDiff = Math.abs(closest.timestamp - marker.timestamp);
        return currentDiff < closestDiff ? candle : closest;
      });

      if (!entryCandle) return;

      // Calculate exact candle dimensions (same logic as in drawCandlesticks)
      const msPerPixel = timeRange / chartWidth;
      let avgCandleInterval = 60 * 60 * 1000;
      if (data.length > 1) {
        const intervals = [];
        for (let i = 1; i < Math.min(data.length, 10); i++) {
          intervals.push(data[i].timestamp - data[i-1].timestamp);
        }
        avgCandleInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      }
      const pixelsPerCandle = avgCandleInterval / msPerPixel;
      let candleWidth = Math.max(1, pixelsPerCandle * 0.7);
      candleWidth = Math.max(1, Math.min(30, candleWidth));

      // Calculate exact candle positions
      const candleX = Math.round(margin.left + ((entryCandle.timestamp - chartBounds.xMin) / timeRange) * chartWidth);
      const highY = Math.round(margin.top + chartHeight - ((entryCandle.high - chartBounds.yMin) / priceRange) * chartHeight);
      const lowY = Math.round(margin.top + chartHeight - ((entryCandle.low - chartBounds.yMin) / priceRange) * chartHeight);

      // Perfect fit dimensions with breathing room on X-axis
      const actualBodyWidth = Math.max(1, Math.round(candleWidth * 0.8));
      const bodyLeft = Math.round(candleX - actualBodyWidth / 2);
      const bodyWidth = actualBodyWidth;

      // Highlight box with X-axis breathing room
      const xBreathingRoom = Math.max(6, candleWidth * 0.3); // More breathing room on sides
      const yPadding = 3; // Minimal Y padding to stay close to candle

      const highlightLeft = bodyLeft - xBreathingRoom;
      const highlightRight = bodyLeft + bodyWidth + xBreathingRoom;
      const highlightTop = highY - yPadding;
      const highlightBottom = lowY + yPadding;
      const highlightWidth = highlightRight - highlightLeft;
      const highlightHeight = highlightBottom - highlightTop;

      // Premium colors for expensive look
      const highlightColor = '#34d399';
      const fillColor = 'rgba(52, 211, 153, 0.08)'; // Low opacity fill
      const glowColor = '#34d399';

      // Professional blur glow effect using shadowBlur
      ctx.save();

      // Outer glow - largest blur
      ctx.shadowColor = glowColor;
      ctx.shadowBlur = 20;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      ctx.strokeStyle = 'rgba(52, 211, 153, 0.3)';
      ctx.lineWidth = 1;
      ctx.strokeRect(highlightLeft, highlightTop, highlightWidth, highlightHeight);

      // Medium glow
      ctx.shadowBlur = 12;
      ctx.strokeStyle = 'rgba(52, 211, 153, 0.4)';
      ctx.lineWidth = 1;
      ctx.strokeRect(highlightLeft, highlightTop, highlightWidth, highlightHeight);

      // Inner glow
      ctx.shadowBlur = 6;
      ctx.strokeStyle = 'rgba(52, 211, 153, 0.6)';
      ctx.lineWidth = 1;
      ctx.strokeRect(highlightLeft, highlightTop, highlightWidth, highlightHeight);

      ctx.restore();

      // Low opacity background fill for premium highlight
      const radius = 4;
      ctx.fillStyle = fillColor;
      ctx.beginPath();
      ctx.moveTo(highlightLeft + radius, highlightTop);
      ctx.lineTo(highlightRight - radius, highlightTop);
      ctx.quadraticCurveTo(highlightRight, highlightTop, highlightRight, highlightTop + radius);
      ctx.lineTo(highlightRight, highlightBottom - radius);
      ctx.quadraticCurveTo(highlightRight, highlightBottom, highlightRight - radius, highlightBottom);
      ctx.lineTo(highlightLeft + radius, highlightBottom);
      ctx.quadraticCurveTo(highlightLeft, highlightBottom, highlightLeft, highlightBottom - radius);
      ctx.lineTo(highlightLeft, highlightTop + radius);
      ctx.quadraticCurveTo(highlightLeft, highlightTop, highlightLeft + radius, highlightTop);
      ctx.closePath();
      ctx.fill();

      // Premium border with subtle inner glow
      ctx.save();
      ctx.shadowColor = glowColor;
      ctx.shadowBlur = 3;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      ctx.strokeStyle = highlightColor;
      ctx.lineWidth = isCurrentTrade ? 2 : 1.5;
      ctx.setLineDash([]);

      // Draw rounded rectangle border
      ctx.beginPath();
      ctx.moveTo(highlightLeft + radius, highlightTop);
      ctx.lineTo(highlightRight - radius, highlightTop);
      ctx.quadraticCurveTo(highlightRight, highlightTop, highlightRight, highlightTop + radius);
      ctx.lineTo(highlightRight, highlightBottom - radius);
      ctx.quadraticCurveTo(highlightRight, highlightBottom, highlightRight - radius, highlightBottom);
      ctx.lineTo(highlightLeft + radius, highlightBottom);
      ctx.quadraticCurveTo(highlightLeft, highlightBottom, highlightLeft, highlightBottom - radius);
      ctx.lineTo(highlightLeft, highlightTop + radius);
      ctx.quadraticCurveTo(highlightLeft, highlightTop, highlightLeft + radius, highlightTop);
      ctx.closePath();
      ctx.stroke();

      ctx.restore();

      // High-quality "Setup #1" label
      if (isCurrentTrade) {
        const labelText = 'Entry';
        const labelPadding = 8;
        const labelHeight = 20;

        // Measure text for perfect sizing
        ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif';
        ctx.textAlign = 'center';
        const textMetrics = ctx.measureText(labelText);
        const labelWidth = textMetrics.width + labelPadding * 2;

        const labelX = candleX - labelWidth / 2;
        const labelY = highlightTop - labelHeight - 8;

        // Premium label with blur effects
        const labelGradient = ctx.createLinearGradient(labelX, labelY, labelX, labelY + labelHeight);
        labelGradient.addColorStop(0, 'rgba(0, 0, 0, 0.95)');
        labelGradient.addColorStop(1, 'rgba(20, 20, 20, 0.9)');

        // Label outer glow with blur
        ctx.save();
        ctx.shadowColor = highlightColor;
        ctx.shadowBlur = 15;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.fillStyle = 'rgba(52, 211, 153, 0.1)';
        ctx.fillRect(labelX, labelY, labelWidth, labelHeight);
        ctx.restore();

        // Label background with premium gradient
        ctx.fillStyle = labelGradient;
        ctx.fillRect(labelX, labelY, labelWidth, labelHeight);

        // Label border with glow
        ctx.save();
        ctx.shadowColor = highlightColor;
        ctx.shadowBlur = 8;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        const borderGradient = ctx.createLinearGradient(labelX, labelY, labelX, labelY + labelHeight);
        borderGradient.addColorStop(0, highlightColor);
        borderGradient.addColorStop(0.5, 'rgba(52, 211, 153, 0.9)');
        borderGradient.addColorStop(1, 'rgba(52, 211, 153, 0.7)');

        ctx.strokeStyle = borderGradient;
        ctx.lineWidth = 1.5;
        ctx.strokeRect(labelX, labelY, labelWidth, labelHeight);
        ctx.restore();

        // Inner highlight for premium depth
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.15)';
        ctx.lineWidth = 1;
        ctx.strokeRect(labelX + 1, labelY + 1, labelWidth - 2, labelHeight - 2);

        // Premium text with glow effect
        ctx.save();
        ctx.shadowColor = highlightColor;
        ctx.shadowBlur = 4;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 11px -apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif';
        ctx.fillText(labelText, candleX, labelY + labelHeight / 2 + 3);
        ctx.restore();

        // Premium indicator arrow with glow
        const arrowSize = 5;
        const arrowY = labelY + labelHeight + 3;

        ctx.save();
        ctx.shadowColor = highlightColor;
        ctx.shadowBlur = 8;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        ctx.fillStyle = highlightColor;
        ctx.beginPath();
        ctx.moveTo(candleX, arrowY + arrowSize);
        ctx.lineTo(candleX - arrowSize, arrowY);
        ctx.lineTo(candleX + arrowSize, arrowY);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
      }
    });
  };

  // Draw Y-axis price labels with TradingView-style precision
  const drawYAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'left';

    const priceRange = chartBounds.yMax - chartBounds.yMin;
    const minPrice = chartBounds.yMin;

    // Calculate optimal number of price levels based on chart height
    const optimalLevels = Math.max(5, Math.min(12, Math.floor(chartHeight / 40)));
    const priceStep = priceRange / (optimalLevels - 1);

    // Determine decimal places based on price range
    const decimalPlaces = priceRange < 1 ? 4 : priceRange < 10 ? 3 : 2;

    for (let i = 0; i < optimalLevels; i++) {
      const price = minPrice + i * priceStep;
      const y = Math.round(margin.top + chartHeight - ((price - minPrice) / priceRange) * chartHeight);

      // Ensure labels are properly positioned within chart bounds
      if (y >= margin.top && y <= margin.top + chartHeight) {
        // Format price with appropriate precision
        const formattedPrice = price >= 1000
          ? price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          : price.toFixed(decimalPlaces);

        // Draw price label with background for better readability
        const text = `$${formattedPrice}`;
        const textWidth = ctx.measureText(text).width;

        // Clean background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        ctx.fillRect(margin.left + chartWidth + 4, y - 7, textWidth + 8, 14);

        // Text with clean white
        ctx.fillStyle = '#ffffff';
        ctx.fillText(text, margin.left + chartWidth + 8, y + 3);
      }
    }
  };

  // Draw X-axis time labels with TradingView-style formatting
  const drawXAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'center';

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const minTime = chartBounds.xMin;

    // Calculate optimal number of time labels based on chart width
    const optimalLabels = Math.max(3, Math.min(8, Math.floor(chartWidth / 120)));
    const timeStep = timeRange / (optimalLabels - 1);

    for (let i = 0; i < optimalLabels; i++) {
      const time = minTime + i * timeStep;
      const x = Math.round(margin.left + ((time - minTime) / timeRange) * chartWidth);

      // Ensure labels are properly positioned within chart bounds
      if (x >= margin.left && x <= margin.left + chartWidth) {
        // Format time based on range - more intelligent formatting
        let timeLabel: string;
        if (timeRange < 24 * 60 * 60 * 1000) { // Less than 1 day
          timeLabel = format(new Date(time), 'HH:mm');
        } else if (timeRange < 7 * 24 * 60 * 60 * 1000) { // Less than 1 week
          timeLabel = format(new Date(time), 'MMM dd HH:mm');
        } else {
          timeLabel = format(new Date(time), 'MMM dd');
        }

        // Draw time label with background
        const textWidth = ctx.measureText(timeLabel).width;

        // Clean background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        ctx.fillRect(x - textWidth / 2 - 4, margin.top + chartHeight + 4, textWidth + 8, 14);

        // Text with clean white
        ctx.fillStyle = '#ffffff';
        ctx.fillText(timeLabel, x, margin.top + chartHeight + 15);
      }
    }
  };

  // Draw TradingView-style crosshair and price/time info
  const drawCrosshair = (
    ctx: CanvasRenderingContext2D,
    crosshair: { x: number; y: number; price: number; time: number },
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    const { x, y, price, time } = crosshair;

    // Draw clean crosshair lines - futuristic style
    ctx.strokeStyle = 'rgba(52, 211, 153, 0.4)'; // Emerald with transparency
    ctx.lineWidth = 1;
    ctx.setLineDash([3, 3]);

    // Horizontal line (price level) - only in chart area
    ctx.beginPath();
    ctx.moveTo(margin.left, y);
    ctx.lineTo(margin.left + chartWidth, y);
    ctx.stroke();

    // Vertical line (time level) - only in chart area
    ctx.beginPath();
    ctx.moveTo(x, margin.top);
    ctx.lineTo(x, margin.top + chartHeight);
    ctx.stroke();

    ctx.setLineDash([]);

    // Price label on Y-axis (right side) - clean futuristic style
    const priceText = `$${price.toFixed(2)}`;
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'left';
    const priceTextWidth = ctx.measureText(priceText).width;

    // Price label with clean background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.9)'; // Clean black background
    ctx.fillRect(margin.left + chartWidth + 1, y - 9, priceTextWidth + 10, 18);

    // Price label border with green accent
    ctx.strokeStyle = '#34d399'; // Emerald-400
    ctx.lineWidth = 1;
    ctx.strokeRect(margin.left + chartWidth + 1, y - 9, priceTextWidth + 10, 18);

    // Price label text
    ctx.fillStyle = '#ffffff';
    ctx.fillText(priceText, margin.left + chartWidth + 6, y + 3);

    // Time label on X-axis (bottom) - clean futuristic style
    const timeText = format(new Date(time), 'HH:mm');
    ctx.textAlign = 'center';
    const timeTextWidth = ctx.measureText(timeText).width;

    // Time label with clean background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
    ctx.fillRect(x - timeTextWidth / 2 - 5, margin.top + chartHeight + 1, timeTextWidth + 10, 18);

    // Time label border with green accent
    ctx.strokeStyle = '#34d399'; // Emerald-400
    ctx.lineWidth = 1;
    ctx.strokeRect(x - timeTextWidth / 2 - 5, margin.top + chartHeight + 1, timeTextWidth + 10, 18);

    // Time label text
    ctx.fillStyle = '#ffffff';
    ctx.fillText(timeText, x, margin.top + chartHeight + 13);
  };

  // Draw indicators (moving averages, RSI, MACD, etc.)
  const drawIndicators = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (indicators.length === 0 || data.length === 0) return;

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const priceRange = chartBounds.yMax - chartBounds.yMin;

    indicators.forEach((indicator, index) => {
      // Set indicator color
      const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'];
      const color = indicator.color || colors[index % colors.length];

      ctx.strokeStyle = color;
      ctx.lineWidth = indicator.lineWidth || 2;
      ctx.setLineDash([]);

      // Draw moving averages as lines
      if (indicator.type === 'moving_average' && indicator.data) {
        ctx.beginPath();
        let firstPoint = true;

        data.forEach((candle, i) => {
          if (indicator.data[i] && !isNaN(indicator.data[i])) {
            const x = margin.left + ((candle.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
            const y = margin.top + chartHeight - ((indicator.data[i] - chartBounds.yMin) / priceRange) * chartHeight;

            if (firstPoint) {
              ctx.moveTo(x, y);
              firstPoint = false;
            } else {
              ctx.lineTo(x, y);
            }
          }
        });

        ctx.stroke();
      }

      // Draw momentum indicators (RSI, Stochastic) in separate panel or as overlay
      if (indicator.type === 'momentum_indicator' && indicator.data) {
        // For now, draw as overlay - could be moved to separate panel later
        ctx.globalAlpha = 0.7;
        ctx.beginPath();
        let firstPoint = true;

        data.forEach((candle, i) => {
          if (indicator.data[i] && !isNaN(indicator.data[i])) {
            const x = margin.left + ((candle.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
            // Scale momentum indicators (0-100) to chart height
            const normalizedValue = chartBounds.yMin + (indicator.data[i] / 100) * priceRange;
            const y = margin.top + chartHeight - ((normalizedValue - chartBounds.yMin) / priceRange) * chartHeight;

            if (firstPoint) {
              ctx.moveTo(x, y);
              firstPoint = false;
            } else {
              ctx.lineTo(x, y);
            }
          }
        });

        ctx.stroke();
        ctx.globalAlpha = 1.0;
      }
    });
  };

  // Draw support and resistance levels
  const drawSupportResistanceLevels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (supportResistanceLevels.length === 0) return;

    const priceRange = chartBounds.yMax - chartBounds.yMin;

    supportResistanceLevels.forEach((level) => {
      const y = margin.top + chartHeight - ((level.price - chartBounds.yMin) / priceRange) * chartHeight;

      // Set color based on type
      const color = level.color || (level.type === 'support' ? '#10b981' : '#ef4444');
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]); // Dashed line

      // Draw horizontal line
      ctx.beginPath();
      ctx.moveTo(margin.left, y);
      ctx.lineTo(margin.left + chartWidth, y);
      ctx.stroke();

      // Draw label
      if (level.label) {
        ctx.fillStyle = color;
        ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
        ctx.textAlign = 'left';
        ctx.fillText(level.label, margin.left + 5, y - 5);
      }
    });

    ctx.setLineDash([]); // Reset line dash
  };

  // Draw trend lines
  const drawTrendLines = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (trendLines.length === 0) return;

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const priceRange = chartBounds.yMax - chartBounds.yMin;

    trendLines.forEach((trendLine) => {
      const startX = margin.left + ((trendLine.startTimestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const startY = margin.top + chartHeight - ((trendLine.startPrice - chartBounds.yMin) / priceRange) * chartHeight;
      const endX = margin.left + ((trendLine.endTimestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const endY = margin.top + chartHeight - ((trendLine.endPrice - chartBounds.yMin) / priceRange) * chartHeight;

      const color = trendLine.color || '#8b5cf6';
      ctx.strokeStyle = color;
      ctx.lineWidth = trendLine.lineWidth || 2;
      ctx.setLineDash([]);

      ctx.beginPath();
      ctx.moveTo(startX, startY);
      ctx.lineTo(endX, endY);
      ctx.stroke();
    });
  };

  // Draw breakout zones
  const drawBreakoutZones = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (breakoutZones.length === 0) return;

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const priceRange = chartBounds.yMax - chartBounds.yMin;

    breakoutZones.forEach((zone) => {
      const x = margin.left + ((zone.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const y = margin.top + chartHeight - ((zone.level - chartBounds.yMin) / priceRange) * chartHeight;

      const color = zone.color || (zone.type === 'bullish' ? '#10b981' : '#ef4444');

      // Draw zone highlight
      ctx.fillStyle = color + '20'; // Add transparency
      ctx.fillRect(x - 10, y - 20, 20, 40);

      // Draw zone border
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.strokeRect(x - 10, y - 20, 20, 40);

      // Draw arrow indicator
      ctx.fillStyle = color;
      ctx.beginPath();
      if (zone.type === 'bullish') {
        ctx.moveTo(x, y - 25);
        ctx.lineTo(x - 5, y - 15);
        ctx.lineTo(x + 5, y - 15);
      } else {
        ctx.moveTo(x, y + 25);
        ctx.lineTo(x - 5, y + 15);
        ctx.lineTo(x + 5, y + 15);
      }
      ctx.closePath();
      ctx.fill();
    });
  };

  // Redraw chart when state changes
  useEffect(() => {
    drawChart();
  }, [drawChart]);

  // React mouse handler - native handlers do the heavy lifting
  const handleReactMouseMove = () => {
    // Native event handlers handle all the logic for TradingView-style behavior
  };

  // Handle clicks on trade markers
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !onNavigateToTrade) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;
    const timeRange = chartBounds.xMax - chartBounds.xMin;

    // Check if click is near any trade marker
    tradeMarkers.forEach((marker) => {
      const x = margin.left + ((marker.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      // Check if click is within 15 pixels of the marker
      const distance = Math.sqrt(Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2));
      if (distance <= 15 && marker.tradeIndex !== undefined) {
        onNavigateToTrade(marker.tradeIndex);
      }
    });
  };

  // Show error state if no data
  if (data.length === 0) {
    return (
      <div className={`w-full bg-[#101010] rounded-lg border border-red-500/20 ${className}`} style={{ height }}>
        <div className="p-3 border-b border-red-500/20">
          <h3 className="text-red-400 text-sm font-medium">{symbol} - Chart Data Error</h3>
        </div>
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-6">
            <div className="text-red-400 text-lg mb-2">📊 No Data Available</div>
            <div className="text-white/60 text-sm">
              Unable to load real market data for this symbol.
              <br />
              Please verify the symbol and try again.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`w-full bg-[#101010] rounded-lg border border-[#1A1A1C] shadow-[0_0_30px_rgba(52,211,153,0.15)] ${className}`} style={{ height }}>
      {/* Chart Canvas */}
      <div className="relative" style={{ height }}>
        <canvas
          ref={canvasRef}
          className="cursor-crosshair w-full"
          style={{ height }}
          onMouseMove={handleReactMouseMove}
          onMouseLeave={handleMouseLeave}
          onClick={handleCanvasClick}
        />

        {/* Reset Controls - positioned away from navigation */}
        {(priceScale !== 1 || timeOffset !== 0 || timeZoom !== 1) && (
          <div className="absolute top-4 right-4 bg-black/90 backdrop-blur-sm rounded-md px-2 py-1 text-white/60 text-xs border border-white/10">
            <button
              onClick={resetView}
              className="hover:text-white/90 transition-colors"
              title="Reset chart view (R)"
            >
              Reset View
            </button>
          </div>
        )}

        {/* Clean status indicator */}
        {crosshair && (
          <div className="absolute top-4 left-4 bg-black/80 backdrop-blur-sm rounded-md px-3 py-2 text-white/70 text-xs border border-white/10">
            <div className="font-mono">${crosshair.price.toFixed(2)}</div>
            <div className="text-white/50 text-[10px]">{format(new Date(crosshair.time), 'MMM dd HH:mm')}</div>
          </div>
        )}

        {/* Minimal help - only show on hover */}
        <div className="absolute bottom-4 right-4 opacity-0 hover:opacity-100 transition-opacity bg-black/90 backdrop-blur-sm rounded-md px-3 py-2 text-white/50 text-xs border border-white/10">
          <div>Drag: Pan | Scroll: Zoom</div>
          <div>Price area: Y-scale</div>
        </div>
      </div>
    </div>
  );
};

export default CleanCandlestickChart;
