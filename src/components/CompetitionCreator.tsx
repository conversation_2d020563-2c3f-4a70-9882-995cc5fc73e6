import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Calendar, DollarSign, Users, Target, Settings, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useCompetitions, CreateCompetitionRequest } from '@/hooks/useCompetitions';
import { useToast } from '@/components/ui/use-toast';
import WhopCompetitionCreator from './WhopCompetitionCreator';
import { checkWhopCompetitionPermissions, WhopCompetitionPermissions } from '@/services/whopCompetitionService';

interface CompetitionCreatorProps {
  onCompetitionCreated?: () => void;
}

const CompetitionCreator: React.FC<CompetitionCreatorProps> = ({
  onCompetitionCreated
}) => {
  const { createCompetition } = useCompetitions();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [whopPermissions, setWhopPermissions] = useState<WhopCompetitionPermissions | null>(null);
  const [loadingPermissions, setLoadingPermissions] = useState(true);

  // Check if we're in localhost development environment
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // Load Whop permissions on component mount
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        const perms = await checkWhopCompetitionPermissions();
        setWhopPermissions(perms);
        console.log('🏆 Whop Competition Permissions:', perms);
      } catch (error) {
        console.error('Error loading Whop permissions:', error);
      } finally {
        setLoadingPermissions(false);
      }
    };

    loadPermissions();
  }, []);

  const [formData, setFormData] = useState<CreateCompetitionRequest>({
    name: '',
    description: '',
    starting_balance: 100000,
    max_participants: undefined,
    entry_fee: 0,
    prize_pool: 0,
    competition_start: '',
    competition_end: '',
    registration_start: '',
    registration_end: '',
    rules: {},
    allowed_securities: undefined,
    position_limits: {}
  });

  const [advancedSettings, setAdvancedSettings] = useState({
    hasRegistrationPeriod: false,
    hasParticipantLimit: false,
    hasEntryFee: false,
    hasPrizePool: false,
    hasSecurityRestrictions: false,
    hasPositionLimits: false
  });

  const handleInputChange = (field: keyof CreateCompetitionRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!formData.name || !formData.competition_start || !formData.competition_end) {
        throw new Error('Please fill in all required fields');
      }

      // Validate dates
      const start = new Date(formData.competition_start);
      const end = new Date(formData.competition_end);
      if (start >= end) {
        throw new Error('Competition end date must be after start date');
      }

      // Clean up optional fields
      const cleanedData: CreateCompetitionRequest = {
        ...formData,
        max_participants: advancedSettings.hasParticipantLimit ? formData.max_participants : undefined,
        entry_fee: advancedSettings.hasEntryFee ? formData.entry_fee : 0,
        prize_pool: advancedSettings.hasPrizePool ? formData.prize_pool : 0,
        registration_start: advancedSettings.hasRegistrationPeriod ? formData.registration_start : undefined,
        registration_end: advancedSettings.hasRegistrationPeriod ? formData.registration_end : undefined,
        allowed_securities: advancedSettings.hasSecurityRestrictions ? formData.allowed_securities : undefined,
        position_limits: advancedSettings.hasPositionLimits ? formData.position_limits : undefined
      };

      await createCompetition(cleanedData);
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        starting_balance: 100000,
        max_participants: undefined,
        entry_fee: 0,
        prize_pool: 0,
        competition_start: '',
        competition_end: '',
        registration_start: '',
        registration_end: '',
        rules: {},
        allowed_securities: undefined,
        position_limits: {}
      });
      
      setIsOpen(false);
      onCompetitionCreated?.();
      
    } catch (error: any) {
      console.error('Error creating competition:', error);
    } finally {
      setLoading(false);
    }
  };

  // Don't render if not in localhost development
  if (!isLocalhost) {
    return null;
  }

  // Show loading state while checking permissions
  if (loadingPermissions) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <div className="animate-spin rounded-full h-14 w-14 border-b-2 border-green-400"></div>
      </div>
    );
  }

  // Show both regular and Whop competition creators
  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
        {/* Whop Competition Creator */}
        {whopPermissions && (whopPermissions.isWhopOwner || whopPermissions.canCreateLocal) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <WhopCompetitionCreator onCompetitionCreated={onCompetitionCreated} />
          </motion.div>
        )}

        {/* Regular Competition Creator */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
        >
          <Button
            onClick={() => setIsOpen(true)}
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 shadow-lg rounded-full h-14 w-14 p-0"
            title="Create Public Competition"
          >
            <Plus className="w-6 h-6" />
          </Button>
        </motion.div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={(e) => e.target === e.currentTarget && setIsOpen(false)}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <Card className="bg-[#0A0A0A] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Target className="w-5 h-5 text-green-400" />
              Create Competition
            </CardTitle>
            <CardDescription>
              Create a new paper trading competition (Development Mode Only)
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Basic Information
                </h3>

                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-white">Competition Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Enter competition name"
                      className="bg-white/[0.05] border-white/[0.1] text-white"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-white">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Describe the competition rules and objectives"
                      className="bg-white/[0.05] border-white/[0.1] text-white"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="starting_balance" className="text-white">Starting Balance *</Label>
                    <Input
                      id="starting_balance"
                      type="number"
                      value={formData.starting_balance}
                      onChange={(e) => handleInputChange('starting_balance', Number(e.target.value))}
                      placeholder="100000"
                      className="bg-white/[0.05] border-white/[0.1] text-white"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Competition Dates */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Competition Schedule
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="competition_start" className="text-white">Start Date & Time *</Label>
                    <Input
                      id="competition_start"
                      type="datetime-local"
                      value={formData.competition_start}
                      onChange={(e) => handleInputChange('competition_start', e.target.value)}
                      className="bg-white/[0.05] border-white/[0.1] text-white"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="competition_end" className="text-white">End Date & Time *</Label>
                    <Input
                      id="competition_end"
                      type="datetime-local"
                      value={formData.competition_end}
                      onChange={(e) => handleInputChange('competition_end', e.target.value)}
                      className="bg-white/[0.05] border-white/[0.1] text-white"
                      required
                    />
                  </div>
                </div>

                {/* Registration Period */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="registration-period"
                    checked={advancedSettings.hasRegistrationPeriod}
                    onCheckedChange={(checked) => 
                      setAdvancedSettings(prev => ({ ...prev, hasRegistrationPeriod: checked }))
                    }
                  />
                  <Label htmlFor="registration-period" className="text-white">
                    Set registration period
                  </Label>
                </div>

                {advancedSettings.hasRegistrationPeriod && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-6">
                    <div>
                      <Label htmlFor="registration_start" className="text-white">Registration Start</Label>
                      <Input
                        id="registration_start"
                        type="datetime-local"
                        value={formData.registration_start}
                        onChange={(e) => handleInputChange('registration_start', e.target.value)}
                        className="bg-white/[0.05] border-white/[0.1] text-white"
                      />
                    </div>

                    <div>
                      <Label htmlFor="registration_end" className="text-white">Registration End</Label>
                      <Input
                        id="registration_end"
                        type="datetime-local"
                        value={formData.registration_end}
                        onChange={(e) => handleInputChange('registration_end', e.target.value)}
                        className="bg-white/[0.05] border-white/[0.1] text-white"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Advanced Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Advanced Settings</h3>

                {/* Participant Limit */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="participant-limit"
                    checked={advancedSettings.hasParticipantLimit}
                    onCheckedChange={(checked) => 
                      setAdvancedSettings(prev => ({ ...prev, hasParticipantLimit: checked }))
                    }
                  />
                  <Label htmlFor="participant-limit" className="text-white">
                    Limit number of participants
                  </Label>
                </div>

                {advancedSettings.hasParticipantLimit && (
                  <div className="ml-6">
                    <Input
                      type="number"
                      value={formData.max_participants || ''}
                      onChange={(e) => handleInputChange('max_participants', Number(e.target.value))}
                      placeholder="Maximum participants"
                      className="bg-white/[0.05] border-white/[0.1] text-white"
                    />
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  {loading ? 'Creating...' : 'Create Competition'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default CompetitionCreator;
