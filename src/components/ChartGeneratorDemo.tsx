import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import SocialMediaChartGenerator, { ChartConfig, ChartDataPoint } from './SocialMediaChartGenerator';
import {
  BrokerageAgentsChart,
  AdvertisingRevenueChart,
  NvidiaNetIncomeChart,
  ComparisonChart,
  AdvertisingComparisonChart,
  createBrokerageAgentsTemplate,
  createNetIncomeTemplate,
  createSampleBrokerageData,
  createSampleNvidiaData
} from './ChartTemplates';

const ChartGeneratorDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('templates');
  const [customConfig, setCustomConfig] = useState<ChartConfig>({
    title: 'Sample Chart',
    type: 'bar',
    data: [
      { name: 'Jan', value: 100 },
      { name: 'Feb', value: 150 },
      { name: 'Mar', value: 200 },
      { name: 'Apr', value: 180 },
      { name: 'May', value: 220 }
    ],
    showValues: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    width: 800,
    height: 600,
    brandText: 'Made with Osis'
  });

  const [csvData, setCsvData] = useState('');

  const handleConfigChange = (key: keyof ChartConfig, value: any) => {
    setCustomConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const parseCsvData = (csv: string): ChartDataPoint[] => {
    const lines = csv.trim().split('\n');
    const data: ChartDataPoint[] = [];
    
    for (let i = 1; i < lines.length; i++) { // Skip header
      const [name, value] = lines[i].split(',');
      if (name && value) {
        data.push({
          name: name.trim(),
          value: parseFloat(value.trim()) || 0
        });
      }
    }
    
    return data;
  };

  const handleCsvImport = () => {
    if (csvData) {
      const data = parseCsvData(csvData);
      handleConfigChange('data', data);
    }
  };

  const sampleCsvData = `Name,Value
Q1 2023,1000
Q2 2023,1200
Q3 2023,1500
Q4 2023,1800
Q1 2024,2100`;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Social Media Chart Generator</h1>
        <p className="text-gray-600">Create professional charts for social media posts</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="templates">Pre-built Templates</TabsTrigger>
          <TabsTrigger value="custom">Custom Chart</TabsTrigger>
          <TabsTrigger value="import">Import Data</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-6">
          <div className="grid gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Brokerage Agents Growth</h3>
              <BrokerageAgentsChart />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">NVIDIA Net Income</h3>
              <NvidiaNetIncomeChart />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Advertising Revenue (Stacked)</h3>
              <AdvertisingRevenueChart />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Apple vs Microsoft - Revenue Comparison (Stacked)</h3>
              <ComparisonChart />
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Advertising Revenue Comparison (Multi-Series Stacked)</h3>
              <AdvertisingComparisonChart />
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="custom" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="p-6 lg:col-span-1">
              <h3 className="text-lg font-semibold mb-4">Chart Configuration</h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Chart Title</Label>
                  <Input
                    id="title"
                    value={customConfig.title}
                    onChange={(e) => handleConfigChange('title', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="subtitle">Subtitle (Optional)</Label>
                  <Input
                    id="subtitle"
                    value={customConfig.subtitle || ''}
                    onChange={(e) => handleConfigChange('subtitle', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="type">Chart Type</Label>
                  <Select value={customConfig.type} onValueChange={(value) => handleConfigChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bar">Bar Chart</SelectItem>
                      <SelectItem value="stacked-bar">Stacked Bar Chart</SelectItem>
                      <SelectItem value="comparison-bar">Comparison Bar Chart</SelectItem>
                      <SelectItem value="line">Line Chart</SelectItem>
                      <SelectItem value="area">Area Chart</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="backgroundColor">Background Color</Label>
                  <Input
                    id="backgroundColor"
                    type="color"
                    value={customConfig.backgroundColor || '#FFFFFF'}
                    onChange={(e) => handleConfigChange('backgroundColor', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="textColor">Text Color</Label>
                  <Input
                    id="textColor"
                    type="color"
                    value={customConfig.textColor || '#000000'}
                    onChange={(e) => handleConfigChange('textColor', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="brandText">Brand Text</Label>
                  <Input
                    id="brandText"
                    value={customConfig.brandText || ''}
                    onChange={(e) => handleConfigChange('brandText', e.target.value)}
                    placeholder="Made with Osis"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="showValues"
                    checked={customConfig.showValues || false}
                    onChange={(e) => handleConfigChange('showValues', e.target.checked)}
                  />
                  <Label htmlFor="showValues">Show Values</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="showGrid"
                    checked={customConfig.showGrid !== false}
                    onChange={(e) => handleConfigChange('showGrid', e.target.checked)}
                  />
                  <Label htmlFor="showGrid">Show Grid</Label>
                </div>
              </div>
            </Card>

            <Card className="p-6 lg:col-span-2">
              <h3 className="text-lg font-semibold mb-4">Chart Preview</h3>
              <SocialMediaChartGenerator 
                config={customConfig}
                onExport={(imageData) => {
                  console.log('Chart exported:', imageData);
                }}
              />
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="import" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Import CSV Data</h3>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="csvData">CSV Data</Label>
                  <Textarea
                    id="csvData"
                    value={csvData}
                    onChange={(e) => setCsvData(e.target.value)}
                    placeholder={sampleCsvData}
                    rows={10}
                  />
                </div>
                <Button onClick={handleCsvImport} className="w-full">
                  Import Data
                </Button>
                <div className="text-sm text-gray-600">
                  <p><strong>Format:</strong> First row should be headers (Name,Value)</p>
                  <p><strong>Example:</strong></p>
                  <pre className="bg-gray-100 p-2 rounded text-xs mt-2">
                    {sampleCsvData}
                  </pre>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Chart Preview</h3>
              <SocialMediaChartGenerator 
                config={customConfig}
                onExport={(imageData) => {
                  console.log('Chart exported:', imageData);
                }}
              />
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChartGeneratorDemo;
