import React from 'react';
import { AreaChart, Area, ResponsiveContainer, defs, linearGradient, stop } from 'recharts';

interface ChartDataPoint {
  date: string;
  value: number;
}

interface MiniAgentChartProps {
  performance: number;
  isPositive: boolean;
  data?: ChartDataPoint[];
  height?: number | string;
}

const MiniAgentChart: React.FC<MiniAgentChartProps> = ({
  performance,
  isPositive,
  data,
  height = 60
}) => {
  // Generate sample data if none provided
  const chartData = data || generateSampleData(isPositive, performance);

  const strokeColor = isPositive ? '#00ff88' : '#ff3632';
  const gradientId = `gradient-${isPositive ? 'positive' : 'negative'}-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="w-full h-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={chartData} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="0%"
                stopColor={strokeColor}
                stopOpacity={0.6}
              />
              <stop
                offset="40%"
                stopColor={strokeColor}
                stopOpacity={0.3}
              />
              <stop
                offset="80%"
                stopColor={strokeColor}
                stopOpacity={0.1}
              />
              <stop
                offset="100%"
                stopColor={strokeColor}
                stopOpacity={0.05}
              />
            </linearGradient>
          </defs>
          <Area
            type="monotone"
            dataKey="value"
            stroke={strokeColor}
            strokeWidth={2}
            fill={`url(#${gradientId})`}
            dot={false}
            activeDot={false}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Generate sample chart data based on performance
function generateSampleData(isPositive: boolean, performance: number): ChartDataPoint[] {
  const points = 30; // More points for smoother curves
  const data: ChartDataPoint[] = [];

  // Start at 100 and end at 100 + performance
  const startValue = 100;
  const endValue = 100 + performance;
  const totalChange = endValue - startValue;

  for (let i = 0; i < points; i++) {
    const progress = i / (points - 1);

    // Create more sophisticated volatility patterns
    const shortTermVolatility = Math.sin(i * 0.6) * (Math.abs(performance) * 0.08);
    const mediumTermVolatility = Math.sin(i * 0.3) * (Math.abs(performance) * 0.12);
    const longTermVolatility = Math.sin(i * 0.15) * (Math.abs(performance) * 0.06);

    // Add some random noise for realism
    const noise = (Math.random() - 0.5) * (Math.abs(performance) * 0.03);

    // Calculate base value with smooth trend
    const smoothProgress = isPositive
      ? Math.pow(progress, 0.8) // Slightly accelerating growth
      : 1 - Math.pow(1 - progress, 1.2); // Decelerating decline

    const baseValue = startValue + (totalChange * smoothProgress);

    // Combine all volatility components
    const totalVolatility = shortTermVolatility + mediumTermVolatility + longTermVolatility + noise;

    let value = baseValue + totalVolatility;

    // Force the last point to be exactly the end value for accuracy
    if (i === points - 1) {
      value = endValue;
    }

    // Ensure we don't go too far from the trend line
    const maxDeviation = Math.abs(performance) * 0.3;
    const expectedValue = startValue + (totalChange * progress);
    value = Math.max(expectedValue - maxDeviation, Math.min(expectedValue + maxDeviation, value));

    data.push({
      date: `Point ${i + 1}`,
      value: Math.round(value * 100) / 100
    });
  }

  return data;
}

export default MiniAgentChart;
