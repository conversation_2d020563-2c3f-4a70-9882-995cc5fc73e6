/**
 * Centralized Whop Type Definitions
 * Single source of truth for all Whop-related types
 */

// Core Whop User interface
export interface WhopUser {
  id: string;
  username: string;
  email?: string;
  profilePicUrl?: string;
  discordId?: string;
  twitterUsername?: string;
  isWhopUser: true;
}

// Whop Access Result interface
export interface WhopAccessResult {
  hasAccess: boolean;
  accessLevel: 'no_access' | 'customer' | 'admin';
  userId: string;
  companyId: string;
  membershipInfo?: any;
  timestamp: string;
  experienceId?: string;
}

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  user?: WhopUser;
  access?: WhopAccessResult;
  error?: string;
  message?: string;
  details?: any;
}

// Whop API Response (legacy compatibility)
export interface WhopApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// Whop App Configuration interface
export interface WhopAppConfig {
  appId: string;
  agentUserId: string;
  companyId: string;
  name: string;
}

// Whop Context Type
export interface WhopContextType {
  // State
  isWhopUser: boolean;
  whopUser: WhopUser | null;
  accessResult: WhopAccessResult | null;
  experienceId: string | null;
  isLoading: boolean;
  error: string | null;
  hasSupabaseSession: boolean;

  // Actions
  refreshWhopAuth: () => Promise<void>;
  clearWhopAuth: () => Promise<void>;
  setExperienceIdAndSave: (experienceId: string) => void;
}

// Whop Supabase User interface
export interface WhopSupabaseUser {
  id: string;
  email: string;
  user_metadata: {
    whop_user_id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    isWhopUser: boolean;
    [key: string]: any;
  };
}

// Whop Auth Result interface
export interface WhopAuthResult {
  success: boolean;
  user?: WhopSupabaseUser;
  session?: any;
  credentials?: {
    email: string;
    password: string;
  };
  error?: string;
}

// Whop Competition interface
export interface WhopCompetition {
  id: string;
  name: string;
  description?: string;
  creator_id: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  status: 'draft' | 'open' | 'active' | 'completed' | 'cancelled';
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
  created_at: string;
  updated_at: string;
  
  // Whop-specific fields
  whop_company_id?: string;
  whop_business_id?: string;
  whop_business_handle?: string;
  is_cross_community?: boolean;
  allowed_whop_communities?: string[];
  competition_scope: 'public' | 'whop_local' | 'whop_cross_community';
}

// Server-side Whop configuration
export interface ServerWhopConfig {
  appId: string;
  apiKey: string;
  agentUserId: string;
  companyId: string;
}

// Whop SDK initialization options
export interface WhopSdkOptions {
  appId: string;
  appApiKey: string;
  onBehalfOfUserId: string;
  companyId: string;
}

// Export all types as a namespace for convenience
export namespace Whop {
  export type User = WhopUser;
  export type AccessResult = WhopAccessResult;
  export type ApiResponse<T = any> = ApiResponse<T>;
  export type AppConfig = WhopAppConfig;
  export type ContextType = WhopContextType;
  export type SupabaseUser = WhopSupabaseUser;
  export type AuthResult = WhopAuthResult;
  export type Competition = WhopCompetition;
  export type ServerConfig = ServerWhopConfig;
  export type SdkOptions = WhopSdkOptions;
}
