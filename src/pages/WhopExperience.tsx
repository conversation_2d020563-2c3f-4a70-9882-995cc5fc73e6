import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import WhopExperiencePage from '@/components/whop/WhopExperiencePage';
import WhopAuthGuard from '@/components/whop/WhopAuthGuard';
import WhopPageLayout from '@/components/whop/WhopPageLayout';
import Home from './Home';
import Trading from './Trading';
import { useWhopUser, useWhop, useWhopAccess } from '@/contexts/WhopContext';
import { Loader2 } from 'lucide-react';
import {
  getExperienceInfo,
  getRouteFromExperienceName,
  type ExperienceInfo
} from '@/utils/experienceUtils';

const WhopExperience: React.FC = () => {
  const { experienceId } = useParams<{ experienceId: string }>();
  const { setExperienceIdAndSave } = useWhop();
  const { accessLevel } = useWhopAccess();
  const [experienceInfo, setExperienceInfo] = useState<ExperienceInfo | null>(null);
  const [isLoadingExperience, setIsLoadingExperience] = useState(true);
  const [experienceError, setExperienceError] = useState<string | null>(null);
  const [experienceIdMessage, setExperienceIdMessage] = useState<string | null>(null);

  // Immediately save the experience ID when we have it
  useEffect(() => {
    if (experienceId) {
      // Format access level for display
      const accessLevelDisplay = accessLevel === 'admin' ? '👑 Admin' :
                                 accessLevel === 'customer' ? '👤 Member' :
                                 accessLevel === 'no_access' ? '🚫 No Access' :
                                 '❓ Unknown';

      // Include experience info if available
      const experienceNameDisplay = experienceInfo?.name ? ` | ${experienceInfo.name}` : '';
      const loadingDisplay = isLoadingExperience ? ' | 🔄 Loading...' : '';

      setExperienceIdMessage(`Experience ID: ${experienceId} | Access: ${accessLevelDisplay}${experienceNameDisplay}${loadingDisplay}`);
      setExperienceIdAndSave(experienceId);
    } else {
      setExperienceIdMessage("No experience ID provided.");
    }

    const timer = setTimeout(() => {
      setExperienceIdMessage(null);
    }, 3000); // Clear message after 3 seconds

    return () => clearTimeout(timer); // Clear timeout on unmount or update
  }, [experienceId, setExperienceIdAndSave, accessLevel, experienceInfo, isLoadingExperience]);

  // Display the message with higher z-index and debugging
  const messageDisplay = experienceIdMessage ? (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white p-3 rounded-md shadow-lg z-[9999] pointer-events-none">
      {experienceIdMessage}
    </div>
  ) : null;

  // Debug logging for message display
  useEffect(() => {
    if (experienceIdMessage) {
      console.log('🔔 Message Display Active:', experienceIdMessage);
    }
  }, [experienceIdMessage]);

  // Fetch experience information to determine routing
  useEffect(() => {
    const fetchExperienceInfo = async () => {
      if (!experienceId) {
        setExperienceError('No experience ID provided');
        setIsLoadingExperience(false);
        return;
      }

      try {
        setIsLoadingExperience(true);

        console.log('� Fetching experience info for:', experienceId);

        // Use the utility function to get experience info
        const experienceInfo = await getExperienceInfo(experienceId);

        if (experienceInfo) {
          setExperienceInfo(experienceInfo);
          console.log('✅ Experience info loaded:', experienceInfo);
        } else {
          throw new Error('Failed to fetch experience info');
        }
      } catch (error) {
        console.error('❌ Error fetching experience info:', error);
        setExperienceError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setIsLoadingExperience(false);
      }
    };

    fetchExperienceInfo();
  }, [experienceId]);

  // Show loading state while fetching experience info
  if (isLoadingExperience) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#0A0A0A]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-gray-400">Loading experience...</p>
        </div>
      </div>
    );
  }

  // Since trade app is ONLY accessed through Whop, we can simplify this logic
  // Default to Trading unless we explicitly know it's OSIS

  let isOsisExperience = false;

  if (experienceInfo) {
    const experienceName = experienceInfo.name.toLowerCase();
    const appName = ((experienceInfo as any).app?.name || '').toLowerCase();
    const detectedApp = ((experienceInfo as any).detectedApp || '').toLowerCase();

    // Only route to OSIS if we explicitly detect OSIS indicators
    isOsisExperience = experienceName.includes('osis') ||
                      appName.includes('osis') ||
                      detectedApp === 'osis';
  }

  // Route to OSIS only if explicitly detected, otherwise default to Trading
  if (isOsisExperience) {
    return <Navigate to="/" replace />;
  }

  // Default: Trade app (since it's only accessed through Whop)
  return (
    <>
      {messageDisplay}
      <WhopExperiencePage>
        <WhopAuthGuard requireAccess={true}>
          <Trading />
        </WhopAuthGuard>
      </WhopExperiencePage>
    </>
  );
};

export default WhopExperience;
