import { supabase } from '@/integrations/supabase/client';
import { Whop<PERSON>ser, WhopAccessResult } from '@/utils/whopAuth';

export interface WhopCompetitionPermissions {
  canCreateLocal: boolean;
  canCreateCrossCommunity: boolean;
  isWhopOwner: boolean;
  isOfficialOsis: boolean;
  whopCompanyId?: string;
  whopBusinessId?: string;
  whopBusinessHandle?: string;
}

export interface WhopCompetitionData {
  name: string;
  description?: string;
  starting_balance: number;
  max_participants?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules?: any;
  allowed_securities?: string[];
  position_limits?: any;
  registration_start?: string;
  registration_end?: string;
  competition_start: string;
  competition_end: string;
  competition_scope: 'public' | 'whop_local' | 'whop_cross_community';
  whop_company_id?: string;
  whop_business_id?: string;
  whop_business_handle?: string;
  is_cross_community?: boolean;
  allowed_whop_communities?: string[];
}

// Official Osis Whop identifiers
const OFFICIAL_OSIS_BUSINESS_ID = 'biz_OGyv6Pz0Le35Fa';
const OFFICIAL_OSIS_HANDLE = 'tryosis';

/**
 * Check if the current user has Whop competition creation permissions
 */
export const checkWhopCompetitionPermissions = async (): Promise<WhopCompetitionPermissions> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return {
        canCreateLocal: false,
        canCreateCrossCommunity: false,
        isWhopOwner: false,
        isOfficialOsis: false
      };
    }

    const whopUserId = user.user_metadata?.whop_user_id;
    const whopAccessLevel = user.user_metadata?.whop_access_level;
    const whopCompanyId = user.user_metadata?.whop_company_id;
    const whopBusinessId = user.user_metadata?.whop_business_id;
    const whopBusinessHandle = user.user_metadata?.whop_business_handle;

    // Check if user is a Whop user with admin access (owner)
    const isWhopOwner = whopUserId && whopAccessLevel === 'admin';
    
    // Check if this is the official Osis Whop
    const isOfficialOsis = whopBusinessId === OFFICIAL_OSIS_BUSINESS_ID || 
                          whopBusinessHandle === OFFICIAL_OSIS_HANDLE;

    return {
      canCreateLocal: !!isWhopOwner,
      canCreateCrossCommunity: !!(isWhopOwner && isOfficialOsis),
      isWhopOwner: !!isWhopOwner,
      isOfficialOsis: !!isOfficialOsis,
      whopCompanyId,
      whopBusinessId,
      whopBusinessHandle
    };
  } catch (error) {
    console.error('Error checking Whop competition permissions:', error);
    return {
      canCreateLocal: false,
      canCreateCrossCommunity: false,
      isWhopOwner: false,
      isOfficialOsis: false
    };
  }
};

/**
 * Create a Whop competition with proper permission validation
 */
export const createWhopCompetition = async (competitionData: WhopCompetitionData) => {
  try {
    // Check permissions first
    const permissions = await checkWhopCompetitionPermissions();
    
    // Validate permission for the requested scope
    if (competitionData.competition_scope === 'whop_local' && !permissions.canCreateLocal) {
      throw new Error('You do not have permission to create local community competitions. Only Whop community owners can create these competitions.');
    }
    
    if (competitionData.competition_scope === 'whop_cross_community' && !permissions.canCreateCrossCommunity) {
      throw new Error('You do not have permission to create cross-community competitions. Only official Osis Whop owners can create these competitions.');
    }

    // Set Whop-specific fields based on user's permissions
    const whopFields: Partial<WhopCompetitionData> = {};
    
    if (competitionData.competition_scope !== 'public') {
      whopFields.whop_company_id = permissions.whopCompanyId;
      whopFields.whop_business_id = permissions.whopBusinessId;
      whopFields.whop_business_handle = permissions.whopBusinessHandle;
    }

    // Call the competition management function
    const { data, error } = await supabase.functions.invoke('whop-competition-management', {
      body: {
        action: 'create_competition',
        ...competitionData,
        ...whopFields
      }
    });

    if (error) {
      throw new Error(error.message || 'Failed to create competition');
    }

    return data;
  } catch (error) {
    console.error('Error creating Whop competition:', error);
    throw error;
  }
};

/**
 * Get competitions filtered by Whop permissions
 */
export const getWhopCompetitions = async () => {
  try {
    const { data, error } = await supabase.functions.invoke('whop-competition-management', {
      body: {
        action: 'get_whop_competitions'
      }
    });

    if (error) {
      throw new Error(error.message || 'Failed to fetch competitions');
    }

    return data;
  } catch (error) {
    console.error('Error fetching Whop competitions:', error);
    throw error;
  }
};

/**
 * Check if user can join a specific competition
 */
export const canJoinWhopCompetition = async (competitionId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.functions.invoke('whop-competition-management', {
      body: {
        action: 'check_join_permission',
        competition_id: competitionId
      }
    });

    if (error) {
      console.error('Error checking join permission:', error);
      return false;
    }

    return data?.canJoin || false;
  } catch (error) {
    console.error('Error checking join permission:', error);
    return false;
  }
};

/**
 * Get user's Whop community information
 */
export const getUserWhopCommunityInfo = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return null;
    }

    return {
      whopUserId: user.user_metadata?.whop_user_id,
      whopAccessLevel: user.user_metadata?.whop_access_level,
      whopCompanyId: user.user_metadata?.whop_company_id,
      whopBusinessId: user.user_metadata?.whop_business_id,
      whopBusinessHandle: user.user_metadata?.whop_business_handle,
      isWhopUser: !!user.user_metadata?.whop_user_id,
      isWhopOwner: user.user_metadata?.whop_access_level === 'admin',
      isOfficialOsis: user.user_metadata?.whop_business_id === OFFICIAL_OSIS_BUSINESS_ID ||
                     user.user_metadata?.whop_business_handle === OFFICIAL_OSIS_HANDLE
    };
  } catch (error) {
    console.error('Error getting user Whop community info:', error);
    return null;
  }
};

/**
 * Validate competition scope and permissions
 */
export const validateCompetitionScope = (
  scope: string,
  permissions: WhopCompetitionPermissions
): { isValid: boolean; errorMessage?: string } => {
  switch (scope) {
    case 'public':
      return { isValid: true };
      
    case 'whop_local':
      if (!permissions.isWhopOwner) {
        return {
          isValid: false,
          errorMessage: 'Only Whop community owners can create local community competitions'
        };
      }
      return { isValid: true };
      
    case 'whop_cross_community':
      if (!permissions.canCreateCrossCommunity) {
        return {
          isValid: false,
          errorMessage: 'Only official Osis Whop owners can create cross-community competitions'
        };
      }
      return { isValid: true };
      
    default:
      return {
        isValid: false,
        errorMessage: 'Invalid competition scope'
      };
  }
};
