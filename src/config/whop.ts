/**
 * Centralized Whop Configuration
 * Single source of truth for all Whop-related configuration
 */

// Environment variable validation
const requiredEnvVars = {
  VITE_WHOP_APP_ID: import.meta.env.VITE_WHOP_APP_ID,
  VITE_WHOP_AGENT_USER_ID: import.meta.env.VITE_WHOP_AGENT_USER_ID,
  VITE_WHOP_COMPANY_ID: import.meta.env.VITE_WHOP_COMPANY_ID,
  VITE_WHOP_INTERMEDIARY_URL: import.meta.env.VITE_WHOP_INTERMEDIARY_URL,
} as const;

// Validate required environment variables
const missingVars = Object.entries(requiredEnvVars)
  .filter(([, value]) => !value)
  .map(([key]) => key);

if (missingVars.length > 0) {
  throw new Error(`Missing required Whop environment variables: ${missingVars.join(', ')}`);
}

// App configuration interface
export interface WhopAppConfig {
  appId: string;
  agentUserId: string;
  companyId: string;
  name: string;
}

// Main Whop configuration
export const WHOP_CONFIG = {
  // Core configuration (required)
  appId: requiredEnvVars.VITE_WHOP_APP_ID,
  agentUserId: requiredEnvVars.VITE_WHOP_AGENT_USER_ID,
  companyId: requiredEnvVars.VITE_WHOP_COMPANY_ID,
  intermediaryUrl: requiredEnvVars.VITE_WHOP_INTERMEDIARY_URL,
  
  // Optional configuration
  intermediaryProductionUrl: import.meta.env.VITE_WHOP_INTERMEDIARY_PRODUCTION_URL,
  tradingAppId: import.meta.env.VITE_TRADING_WHOP_APP_ID,
  tradingAgentUserId: import.meta.env.VITE_TRADING_WHOP_AGENT_USER_ID,
  tradingCompanyId: import.meta.env.VITE_TRADING_WHOP_COMPANY_ID,
} as const;

// App configurations
export const WHOP_APP_CONFIGS: Record<string, WhopAppConfig> = {
  osis: {
    appId: WHOP_CONFIG.appId,
    agentUserId: WHOP_CONFIG.agentUserId,
    companyId: WHOP_CONFIG.companyId,
    name: 'OSIS'
  },
  trading: {
    appId: WHOP_CONFIG.tradingAppId || WHOP_CONFIG.appId,
    agentUserId: WHOP_CONFIG.tradingAgentUserId || WHOP_CONFIG.agentUserId,
    companyId: WHOP_CONFIG.tradingCompanyId || WHOP_CONFIG.companyId,
    name: 'Trading'
  }
};

// Server-side configuration (for API routes)
export const getServerWhopConfig = () => {
  // Use environment variables directly for server-side
  const serverConfig = {
    appId: process.env.VITE_WHOP_APP_ID || process.env.NEXT_PUBLIC_WHOP_APP_ID,
    apiKey: process.env.WHOP_API_KEY,
    agentUserId: process.env.VITE_WHOP_AGENT_USER_ID || process.env.NEXT_PUBLIC_WHOP_AGENT_USER_ID,
    companyId: process.env.VITE_WHOP_COMPANY_ID || process.env.NEXT_PUBLIC_WHOP_COMPANY_ID,
  };

  // Validate server configuration
  const requiredServerVars = ['appId', 'apiKey', 'agentUserId', 'companyId'];
  const missingServerVars = requiredServerVars.filter(key => !serverConfig[key as keyof typeof serverConfig]);
  
  if (missingServerVars.length > 0) {
    throw new Error(`Missing required server Whop configuration: ${missingServerVars.join(', ')}`);
  }

  return serverConfig;
};

// App detection logic
export function detectCurrentApp(): string {
  if (typeof window === 'undefined') return 'trading';
  
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  
  // Check URL patterns
  if (hostname.includes('osis') || pathname.includes('/osis')) {
    return 'osis';
  }
  
  // Default to trading app for Whop access
  return 'trading';
}

// Get current app configuration
export function getCurrentAppConfig(): WhopAppConfig {
  const appKey = detectCurrentApp();
  const config = WHOP_APP_CONFIGS[appKey];
  
  if (!config) {
    throw new Error(`Unknown app key: ${appKey}`);
  }
  
  return config;
}

// Get app configuration by key
export function getAppConfig(appKey: string): WhopAppConfig {
  const config = WHOP_APP_CONFIGS[appKey];
  
  if (!config) {
    throw new Error(`Unknown app key: ${appKey}`);
  }
  
  return config;
}

// Validate app configuration
export function validateAppConfig(appKey: string): boolean {
  try {
    const config = getAppConfig(appKey);
    const requiredFields = ['appId', 'agentUserId', 'companyId'];
    
    const missingFields = requiredFields.filter(field => !config[field as keyof WhopAppConfig]);
    
    if (missingFields.length > 0) {
      console.error(`Missing required fields for ${config.name} app:`, missingFields);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Failed to validate app config for ${appKey}:`, error);
    return false;
  }
}

// Environment detection helpers
export function isInWhopEnvironment(): boolean {
  if (typeof window === 'undefined') return false;
  
  return !!(
    window.location.search.includes('whop') ||
    window.location.hostname.includes('whop') ||
    document.referrer.includes('whop.com') ||
    window.parent !== window
  );
}

export function isInWhopIframeContext(): boolean {
  if (typeof window === 'undefined') return false;
  return window.parent !== window;
}

// Get intermediary server URL
export function getIntermediaryServerUrl(): string {
  const isDevelopment = import.meta.env.DEV;
  
  if (isDevelopment) {
    return WHOP_CONFIG.intermediaryUrl;
  }
  
  return WHOP_CONFIG.intermediaryProductionUrl || WHOP_CONFIG.intermediaryUrl;
}

// Export default configuration
export default WHOP_CONFIG;
